[Unit]
Description=ARGUS Controller Core Service
Documentation=https://gitlab.devforyou.pl/avotech/controller_core
After=network-online.target
Wants=network-online.target
After=systemd-resolved.service
After=avahi-daemon.service
Requires=network-online.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/root/argus_controller_ws

# Environment setup
Environment=HOME=/root
Environment=USER=root
Environment=ROS_DOMAIN_ID=42
Environment=ROS_LOCALHOST_ONLY=0
Environment=RMW_IMPLEMENTATION=rmw_fastrtps_cpp
Environment=RCUTILS_LOGGING_SEVERITY_THRESHOLD=INFO
Environment=RCUTILS_COLORIZED_OUTPUT=1

# ROS2 and workspace setup
ExecStartPre=/bin/bash -c 'source /opt/ros/jazzy/setup.bash'
ExecStartPre=/bin/bash -c 'if [ -f /root/argus_controller_ws/install/setup.bash ]; then source /root/argus_controller_ws/install/setup.bash; fi'

# Main service command
ExecStart=/bin/bash -c 'source /opt/ros/jazzy/setup.bash && if [ -f /root/argus_controller_ws/install/setup.bash ]; then source /root/argus_controller_ws/install/setup.bash; fi && ros2 launch controller_core controller.launch.py'

# Fallback command if launch file doesn't exist
ExecStartPost=/bin/bash -c 'if [ $? -ne 0 ]; then echo "Launch file not found, starting basic ROS2 nodes"; ros2 run controller_core controller_node; fi'

# Service management
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# Process management
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=controller_core

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=false
ReadWritePaths=/root/argus_controller
ReadWritePaths=/root/argus_controller_ws
ReadWritePaths=/var/log/argus_controller
ReadWritePaths=/tmp

# Resource limits
LimitNOFILE=65536
LimitNPROC=32768

# Network settings (allow network access for ROS2)
RestrictAddressFamilies=AF_UNIX AF_INET AF_INET6 AF_NETLINK
IPAddressDeny=any
IPAddressAllow=localhost
IPAddressAllow=link-local
IPAddressAllow=multicast

[Install]
WantedBy=multi-user.target
Also=avahi-daemon.service
