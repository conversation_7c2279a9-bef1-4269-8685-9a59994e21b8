# ARGUS Controller Network Configuration
# Przykładowa konfiguracja Netplan dla kontrolera ARGUS
# Skopiuj do /etc/netplan/ i dostosuj do swojego środowiska

network:
  version: 2
  renderer: NetworkManager
  
  # Konfiguracja Ethernet
  ethernets:
    # Gł<PERSON>ny interfejs Ethernet
    eth0:
      dhcp4: true
      dhcp6: false
      optional: true
      # Statyczna konfiguracja IP (odkomentuj jeśli potrzebna)
      # addresses:
      #   - *************/24
      # gateway4: ***********
      # nameservers:
      #   addresses:
      #     - *******
      #     - *******
      #   search:
      #     - local
      
    # Dodatkowy interfejs (jeśli dostępny)
    eth1:
      dhcp4: true
      dhcp6: false
      optional: true
      
  # Konfiguracja WiFi
  wifis:
    # Główny interfejs WiFi
    wlan0:
      dhcp4: true
      dhcp6: false
      optional: true
      access-points:
        # <PERSON><PERSON><PERSON> laboratory<PERSON>a
        "ARGUS-Lab":
          password: "laboratory2024"
          
        # <PERSON><PERSON><PERSON> biurowa
        "ARGUS-Office":
          password: "office2024"
          
        # <PERSON><PERSON><PERSON> do<PERSON> (przykład)
        "HomeNetwork":
          password: "homepassword"
          
        # Si<PERSON><PERSON> mobilna (hotspot)
        "Mobile-Hotspot":
          password: "mobilepass"
          
      # Konfiguracja zaawansowana WiFi
      # regulatory-domain: PL
      # band: 2.4GHz
      # channel: 6
      
  # Konfiguracja mostków (bridges) - dla zaawansowanych zastosowań
  bridges:
    # Most dla kontenerów Docker
    br-docker:
      dhcp4: false
      addresses:
        - **********/16
      parameters:
        stp: false
        forward-delay: 0
        
  # Konfiguracja VLAN (jeśli potrzebna)
  vlans:
    # VLAN dla robotów
    vlan-robots:
      id: 100
      link: eth0
      addresses:
        - *************/24
        
    # VLAN dla zarządzania
    vlan-mgmt:
      id: 200
      link: eth0
      addresses:
        - *************/24
