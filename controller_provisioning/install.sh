#!/bin/bash

# ARGUS Controller Provisioning Script
# Automatyczna instalacja i konfiguracja kontrolera ARGUS na Ubuntu 24.04 LTS (headless)
# Wymaga: Ubuntu 24.04 LTS Server, Raspberry Pi 4/5, połączenie internetowe

set -e  # Zatrzymaj przy błędzie

# Kolory dla outputu
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Ścieżki
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_DIR="$SCRIPT_DIR/scripts"
FILES_DIR="$SCRIPT_DIR/files"
SERVICES_DIR="$SCRIPT_DIR/services"
NETWORK_DIR="$SCRIPT_DIR/network"
CONFIG_DIR="$SCRIPT_DIR/config"

# Zmienne konfiguracyjne
CURRENT_USER=${SUDO_USER:-$USER}
USER_HOME=$(eval echo ~$CURRENT_USER)
CONTROLLER_ROOT="/root/argus_controller"
CONTROLLER_WS="/root/argus_controller_ws"

# Funkcje pomocnicze
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# Funkcja sprawdzająca wersję Ubuntu
check_ubuntu_version() {
    if [ ! -f /etc/os-release ]; then
        log_error "Nie znaleziono pliku /etc/os-release"
        return 1
    fi

    local os_id=$(grep '^ID=' /etc/os-release | cut -d '=' -f 2 | tr -d '"')
    local os_version=$(grep '^VERSION_ID=' /etc/os-release | cut -d '=' -f 2 | tr -d '"')
    local os_name=$(grep '^PRETTY_NAME=' /etc/os-release | cut -d '=' -f 2 | tr -d '"')

    if [ "$os_id" != "ubuntu" ]; then
        log_error "To nie jest Ubuntu! To jest: $os_name"
        return 1
    fi

    if [ "$os_version" == "24.04" ]; then
        log_success "System to Ubuntu 24.04 LTS ($os_name)"
        return 0
    else
        log_warning "To nie jest Ubuntu 24.04 LTS. Obecna wersja: $os_name"
        log_warning "Skrypt może nie działać poprawnie na innych wersjach"
        read -p "Czy chcesz kontynuować? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            return 0
        else
            return 1
        fi
    fi
}

# Funkcja sprawdzająca uprawnienia root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "Ten skrypt musi być uruchomiony jako root (sudo)"
        exit 1
    fi
}

# Funkcja sprawdzająca połączenie internetowe
check_internet() {
    log_info "Sprawdzanie połączenia internetowego..."
    if ping -c 1 google.com &> /dev/null; then
        log_success "Połączenie internetowe działa"
        return 0
    else
        log_error "Brak połączenia internetowego"
        return 1
    fi
}

# Funkcja sprawdzająca wymagania systemowe
check_requirements() {
    log_info "Sprawdzanie wymagań systemowych..."
    
    # Sprawdź RAM
    local ram_gb=$(free -g | awk '/^Mem:/{print $2}')
    if [ "$ram_gb" -lt 4 ]; then
        log_warning "Mało RAM: ${ram_gb}GB (zalecane minimum 4GB)"
    else
        log_success "RAM: ${ram_gb}GB"
    fi
    
    # Sprawdź miejsce na dysku
    local disk_gb=$(df -BG / | awk 'NR==2{print $4}' | sed 's/G//')
    if [ "$disk_gb" -lt 20 ]; then
        log_warning "Mało miejsca na dysku: ${disk_gb}GB (zalecane minimum 20GB)"
    else
        log_success "Wolne miejsce na dysku: ${disk_gb}GB"
    fi
    
    # Sprawdź architekturę
    local arch=$(uname -m)
    log_info "Architektura: $arch"
    
    return 0
}

# Funkcja wykonująca skrypt z logowaniem
run_script() {
    local script_name="$1"
    local script_path="$SCRIPTS_DIR/$script_name"

    if [ ! -f "$script_path" ]; then
        log_error "Skrypt $script_name nie istnieje w $script_path"
        return 1
    fi

    log_step "Wykonywanie skryptu: $script_name"
    chmod +x "$script_path"

    # Uruchom skrypt z przekazaniem zmiennych środowiskowych
    if SUDO_USER="$CURRENT_USER" bash "$script_path"; then
        log_success "Skrypt $script_name wykonany pomyślnie"
        return 0
    else
        log_error "Błąd podczas wykonywania skryptu $script_name"
        return 1
    fi
}

# Funkcja wyświetlająca podsumowanie instalacji
show_summary() {
    log_success "Instalacja ARGUS Controller zakończona pomyślnie!"
    echo ""
    echo -e "${CYAN}=== PODSUMOWANIE INSTALACJI ===${NC}"
    echo ""
    echo "Zainstalowane komponenty:"
    echo "✓ Ubuntu 24.04 LTS Server (headless) - podstawowa konfiguracja"
    echo "✓ ROS2 Jazzy Base - z natywnych pakietów Ubuntu"
    echo "✓ Python, Node.js, Docker - środowiska deweloperskie"
    echo "✓ Narzędzia sieciowe i monitoring (headless)"
    echo "✓ Usługi systemowe kontrolera"
    echo ""
    echo "Katalogi kontrolera:"
    echo "• $CONTROLLER_ROOT - główny katalog"
    echo "• $CONTROLLER_WS - workspace ROS2"
    echo "• $CONTROLLER_ROOT/src - kod źródłowy"
    echo "• /opt/argus_controller - pliki systemowe"
    echo "• /var/log/argus_controller - logi"
    echo ""
    echo "Dostępne komendy:"
    echo "• controller-manage start|stop|restart|status"
    echo "• controller-diag - diagnostyka systemu"
    echo "• controller-logs - podgląd logów"
    echo ""
    echo "Porty sieciowe:"
    echo "• 8080 - Controller API"
    echo "• 8081 - WebSocket"
    echo "• 3000 - Web Interface"
    echo "• 7400-7500 - ROS2 Communication"
    echo ""
    echo "Następne kroki:"
    echo "1. Zrestartuj system: sudo reboot"
    echo "2. Sprawdź status: controller-diag"
    echo "3. Zbuduj workspace: cd $CONTROLLER_WS && colcon build"
    echo "4. Uruchom kontroler: controller-manage start"
    echo ""
    echo -e "${GREEN}System jest gotowy do pracy jako kontroler ARGUS!${NC}"
}

# Funkcja główna instalacji
install_controller() {
    log_step "Rozpoczynanie instalacji ARGUS Controller..."

    # Kolejność wykonywania skryptów
    local scripts=(
        "01-base.sh"
        "02-env.sh"
        "03-deps.sh"
        "04-ros2.sh"
        "05-network.sh"
        "06-services.sh"
    )

    local total_scripts=${#scripts[@]}
    local current_script=0

    for script in "${scripts[@]}"; do
        current_script=$((current_script + 1))
        echo ""
        echo -e "${CYAN}=== KROK $current_script/$total_scripts: $script ===${NC}"
        
        if ! run_script "$script"; then
            log_error "Instalacja przerwana z powodu błędu w skrypcie $script"
            echo ""
            echo "Aby kontynuować instalację później, uruchom:"
            echo "sudo $0 --continue-from $script"
            exit 1
        fi
        
        log_success "Krok $current_script/$total_scripts zakończony"
        sleep 2 
    done

    show_summary
}

# Funkcja pomocy
show_help() {
    echo "ARGUS Controller Provisioning Script"
    echo ""
    echo "Użycie:"
    echo "  sudo $0 [opcje]"
    echo ""
    echo "Opcje:"
    echo "  -h, --help              Wyświetl tę pomoc"
    echo "  -c, --check             Sprawdź tylko wymagania systemowe"
    echo "  -v, --verbose           Tryb szczegółowy"
    echo "  --no-reboot            Nie restartuj po instalacji"
    echo "  --continue-from SCRIPT  Kontynuuj od określonego skryptu"
    echo ""
    echo "Przykłady:"
    echo "  sudo $0                 # Pełna instalacja"
    echo "  sudo $0 --check         # Sprawdź wymagania"
    echo "  sudo $0 --no-reboot     # Instalacja bez restartu"
    echo ""
}

# Funkcja główna
main() {
    # Parsowanie argumentów
    local check_only=false
    local no_reboot=false
    local continue_from=""
    local verbose=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -c|--check)
                check_only=true
                shift
                ;;
            --no-reboot)
                no_reboot=true
                shift
                ;;
            --continue-from)
                continue_from="$2"
                shift 2
                ;;
            -v|--verbose)
                verbose=true
                set -x
                shift
                ;;
            *)
                log_error "Nieznana opcja: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # Banner
    clear
    echo -e "${BLUE}"
    echo "  █████╗ ██████╗  ██████╗ ██╗   ██╗███████╗"
    echo " ██╔══██╗██╔══██╗██╔════╝ ██║   ██║██╔════╝"
    echo " ███████║██████╔╝██║  ███╗██║   ██║███████╗"
    echo " ██╔══██║██╔══██╗██║   ██║██║   ██║╚════██║"
    echo " ██║  ██║██║  ██║╚██████╔╝╚██████╔╝███████║"
    echo " ╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝  ╚═════╝ ╚══════╝"
    echo ""
    echo -e "${CYAN}        CONTROLLER PROVISIONING${NC}"
    echo -e "${YELLOW}        Ubuntu 24.04 LTS (Headless) + ROS2 Jazzy${NC}"
    echo ""

    # Sprawdzenia wstępne
    check_root

    if ! check_ubuntu_version; then
        exit 1
    fi

    if ! check_internet; then
        log_error "Wymagane jest połączenie internetowe do instalacji"
        exit 1
    fi

    check_requirements

    # Tryb sprawdzania
    if [ "$check_only" = true ]; then
        log_success "Sprawdzenie wymagań zakończone"
        exit 0
    fi

    echo ""
    log_info "Użytkownik: $CURRENT_USER"
    log_info "Katalog domowy: $USER_HOME"
    log_info "Katalog kontrolera: $CONTROLLER_ROOT"
    echo ""

    # Potwierdzenie instalacji
    read -p "Czy chcesz rozpocząć instalację ARGUS Controller? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Instalacja anulowana przez użytkownika"
        exit 0
    fi

    # Rozpocznij instalację
    install_controller

    # Restart systemu (jeśli nie wyłączony)
    if [ "$no_reboot" != true ]; then
        echo ""
        log_info "System zostanie zrestartowany za 10 sekund..."
        log_info "Aby anulować restart, naciśnij Ctrl+C"
        sleep 10
        reboot
    else
        echo ""
        log_info "Instalacja zakończona. Zalecany restart systemu: sudo reboot"
    fi
}

main "$@"
