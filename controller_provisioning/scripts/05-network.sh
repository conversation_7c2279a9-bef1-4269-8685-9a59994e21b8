#!/bin/bash
# 05-network.sh - Konfiguracja sieci dla kontrolera ARGUS

set -e

echo "=== Konfiguracja sieci kontrolera ARGUS ==="

# Sprawdzenie czy to Ubuntu Desktop czy Server
if dpkg -l | grep -q ubuntu-desktop; then
    UBUNTU_TYPE="desktop"
    echo "Wykryto Ubuntu Desktop - używam NetworkManager"
else
    UBUNTU_TYPE="server"
    echo "Wykryto Ubuntu Server - używam Netplan"
fi

# Instalacja narzędzi sieciowych
echo "Instalacja narzędzi sieciowych..."
apt update
apt install -y \
    network-manager \
    networkd-dispatcher \
    netplan.io \
    bridge-utils \
    vlan \
    ifupdown \
    resolvconf \
    dnsutils \
    avahi-daemon \
    avahi-utils

# Konfiguracja NetworkManager (dla Desktop)
if [ "$UBUNTU_TYPE" = "desktop" ]; then
    echo "Konfiguracja NetworkManager..."
    
    # Upewnienie się, że NetworkManager jest aktywny
    systemctl enable NetworkManager
    systemctl start NetworkManager
    
    # Konfiguracja NetworkManager dla ROS2
    cat > /etc/NetworkManager/conf.d/99-argus-controller.conf << 'EOF'
[main]
# Konfiguracja dla ARGUS Controller
dns=systemd-resolved

[connection]
# Automatyczne połączenie
autoconnect-priority=100

[device]
# Zarządzanie wszystkimi interfejsami
wifi.scan-rand-mac-address=no
EOF

    systemctl restart NetworkManager
fi

# Konfiguracja Netplan (dla Server lub jako backup)
echo "Konfiguracja Netplan..."
mkdir -p /etc/netplan

# Tworzenie konfiguracji Netplan dla kontrolera
cat > /etc/netplan/99-argus-controller.yaml << 'EOF'
# ARGUS Controller Network Configuration
network:
  version: 2
  renderer: NetworkManager
  
  ethernets:
    # Konfiguracja dla interfejsu Ethernet (automatyczna)
    eth0:
      dhcp4: true
      dhcp6: false
      optional: true
      
  wifis:
    # Konfiguracja WiFi (jeśli dostępne)
    wlan0:
      dhcp4: true
      dhcp6: false
      optional: true
      access-points:
        # Przykładowa konfiguracja - należy dostosować
        "ARGUS-Network":
          password: "argus123456"
        "ARGUS-Lab":
          password: "laboratory2024"
EOF

# Konfiguracja systemd-resolved
echo "Konfiguracja systemd-resolved..."
cat > /etc/systemd/resolved.conf << 'EOF'
[Resolve]
DNS=******* *******
FallbackDNS=******* *******
Domains=local
LLMNR=yes
MulticastDNS=yes
DNSSEC=allow-downgrade
DNSOverTLS=no
Cache=yes
DNSStubListener=yes
ReadEtcHosts=yes
EOF

systemctl restart systemd-resolved

# Konfiguracja Avahi (mDNS/Zeroconf)
echo "Konfiguracja Avahi dla wykrywania robotów..."
cat > /etc/avahi/avahi-daemon.conf << 'EOF'
[server]
host-name=argus-controller
domain-name=local
browse-domains=local
use-ipv4=yes
use-ipv6=no
allow-interfaces=eth0,wlan0
deny-interfaces=lo,docker0
check-response-ttl=no
use-iff-running=no
enable-dbus=yes
disallow-other-stacks=no
allow-point-to-point=no
cache-entries-max=4096
clients-max=4096
objects-per-client-max=1024
entries-per-entry-group-max=32
ratelimit-interval-usec=1000000
ratelimit-burst=1000

[wide-area]
enable-wide-area=yes

[publish]
disable-publishing=no
disable-user-service-publishing=no
add-service-cookie=no
publish-addresses=yes
publish-hinfo=yes
publish-workstation=no
publish-domain=yes
publish-dns-servers=no
publish-resolv-conf-dns-servers=yes
publish-aaaa-on-ipv4=yes
publish-a-on-ipv6=no

[reflector]
enable-reflector=no
reflect-ipv=no

[rlimits]
rlimit-as=
rlimit-core=0
rlimit-data=8388608
rlimit-fsize=0
rlimit-nofile=768
rlimit-stack=8388608
rlimit-nproc=3
EOF

# Tworzenie usługi Avahi dla kontrolera
echo "Tworzenie usługi Avahi..."
cat > /etc/avahi/services/argus-controller.service << 'EOF'
<?xml version="1.0" standalone='no'?>
<!DOCTYPE service-group SYSTEM "avahi-service.dtd">
<service-group>
  <name replace-wildcards="yes">ARGUS Controller on %h</name>
  <service>
    <type>_argus-controller._tcp</type>
    <port>8080</port>
    <txt-record>version=1.0</txt-record>
    <txt-record>type=controller</txt-record>
    <txt-record>ros2_domain=42</txt-record>
  </service>
  <service>
    <type>_http._tcp</type>
    <port>3000</port>
    <txt-record>path=/</txt-record>
    <txt-record>description=ARGUS Controller Web Interface</txt-record>
  </service>
</service-group>
EOF

# Restart usług sieciowych
echo "Restart usług sieciowych..."
systemctl enable avahi-daemon
systemctl restart avahi-daemon

# Konfiguracja firewall dla kontrolera
echo "Konfiguracja firewall dla kontrolera..."
ufw --force enable

# Porty dla ROS2
ufw allow 7400:7500/tcp comment "ROS2 TCP ports"
ufw allow 7400:7500/udp comment "ROS2 UDP ports"

# Porty dla aplikacji kontrolera
ufw allow 8080/tcp comment "Controller API"
ufw allow 8081/tcp comment "Controller WebSocket"
ufw allow 3000/tcp comment "Web Interface"

# Porty dla komunikacji z robotami
ufw allow 11311/tcp comment "ROS Master (legacy)"
ufw allow 5353/udp comment "mDNS/Avahi"

# SSH (już powinien być dozwolony)
ufw allow ssh

# Konfiguracja multicast dla ROS2
echo "Konfiguracja multicast dla ROS2..."
# Dodanie reguł iptables dla multicast
iptables -I INPUT -m pkttype --pkt-type multicast -j ACCEPT
iptables -I OUTPUT -m pkttype --pkt-type multicast -j ACCEPT

# Zapisanie reguł iptables
apt install -y iptables-persistent
netfilter-persistent save

# Konfiguracja sysctl dla sieci
echo "Konfiguracja parametrów sieciowych..."
cat >> /etc/sysctl.conf << 'EOF'

# ARGUS Controller network optimizations
# Zwiększenie bufferów sieciowych dla ROS2
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.core.rmem_default = 65536
net.core.wmem_default = 65536
net.ipv4.udp_mem = 102400 873800 16777216
net.ipv4.udp_rmem_min = 8192
net.ipv4.udp_wmem_min = 8192

# Multicast settings
net.ipv4.igmp_max_memberships = 100
net.ipv4.igmp_max_msf = 100

# TCP settings
net.ipv4.tcp_congestion_control = bbr
net.core.default_qdisc = fq
EOF

sysctl -p

# Test konfiguracji sieci
echo "Test konfiguracji sieci..."

# Test podstawowej łączności
if ping -c 1 google.com &> /dev/null; then
    echo "✓ Połączenie internetowe działa"
else
    echo "⚠ Brak połączenia internetowego"
fi

# Test mDNS
if avahi-browse -t _http._tcp 2>/dev/null | grep -q "argus-controller"; then
    echo "✓ Avahi/mDNS działa"
else
    echo "⚠ Avahi/mDNS może nie działać poprawnie"
fi

# Informacje o interfejsach sieciowych
echo ""
echo "Aktywne interfejsy sieciowe:"
ip addr show | grep -E "^[0-9]+:|inet " | grep -v "127.0.0.1"

echo "=== Konfiguracja sieci zakończona ==="
echo ""
echo "Skonfigurowane komponenty:"
echo "- NetworkManager/Netplan"
echo "- systemd-resolved (DNS)"
echo "- Avahi (mDNS/Zeroconf)"
echo "- Firewall (ufw) z portami dla ROS2"
echo "- Optymalizacje sieciowe dla ROS2"
echo ""
echo "Usługi sieciowe kontrolera:"
echo "- Port 8080: Controller API"
echo "- Port 8081: WebSocket"
echo "- Port 3000: Web Interface"
echo "- Porty 7400-7500: ROS2 Communication"
echo ""
echo "Aby sprawdzić status sieci:"
echo "  ip addr show"
echo "  systemctl status NetworkManager"
echo "  avahi-browse -t _argus-controller._tcp"
