#!/bin/bash
# 01-base.sh - Podstawowa konfiguracja systemu Ubuntu 24.04 i instalacja pakietów

set -e

echo "=== Aktualizacja systemu Ubuntu 24.04 i instalacja podstawowych pakietów ==="

# Sprawdzenie wersji Ubuntu
echo "Sprawdzanie wersji systemu..."
. /etc/os-release
echo "Wykryto system: $PRETTY_NAME"

if [[ "$VERSION_ID" != "24.04" ]]; then
    echo "UWAGA: Ten skrypt jest zoptymalizowany dla Ubuntu 24.04 LTS"
    echo "Obecna wersja: $PRETTY_NAME"
    echo "Kontynuuję instalację, ale mogą wystąpić problemy..."
fi

# Aktualizacja listy pakietów
echo "Aktualizacja listy pakietów..."
apt update -qq

# Upgrade systemu
echo "Aktualizacja systemu..."
apt upgrade -y

# Instalacja podstawowych narzędzi
echo "Instalacja podstawowych narzędzi..."
apt install -y \
    git \
    curl \
    wget \
    vim \
    nano \
    htop \
    tree \
    unzip \
    zip \
    build-essential \
    cmake \
    pkg-config \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release

# Narzędzia deweloperskie
echo "Instalacja narzędzi deweloperskich..."
apt install -y \
    python3-dev \
    python3-pip \
    python3-venv \
    python3-setuptools \
    python3-wheel \
    nodejs \
    npm \
    default-jdk

# Biblioteki multimedialne (headless - tylko przetwarzanie)
echo "Instalacja bibliotek multimedialnych (headless)..."
apt install -y \
    libjpeg-dev \
    libtiff5-dev \
    libpng-dev \
    libavcodec-dev \
    libavformat-dev \
    libswscale-dev \
    libv4l-dev \
    libxvidcore-dev \
    libx264-dev

# Biblioteki matematyczne i naukowe
echo "Instalacja bibliotek matematycznych..."
apt install -y \
    libatlas-base-dev \
    gfortran \
    libeigen3-dev \
    libopencv-dev \
    python3-opencv

# Narzędzia sieciowe
echo "Instalacja narzędzi sieciowych..."
apt install -y \
    net-tools \
    iputils-ping \
    traceroute \
    nmap \
    netcat-openbsd \
    openssh-server \
    ufw

# Konfiguracja SSH (jeśli nie jest aktywny)
echo "Konfiguracja SSH..."
systemctl enable ssh
systemctl start ssh

# Konfiguracja firewall (podstawowa)
echo "Konfiguracja podstawowego firewall..."
ufw --force enable
ufw allow ssh
ufw allow 22/tcp

# Dodanie użytkownika do grup (jeśli istnieje użytkownik argusik)
echo "Konfiguracja uprawnień użytkownika..."
if id "argusik" &>/dev/null; then
    usermod -a -G sudo,dialout,video,audio argusik
    echo "Użytkownik argusik dodany do grup: sudo, dialout, video, audio"
else
    echo "Użytkownik argusik nie istnieje - pomijam konfigurację grup"
fi

# Konfiguracja swappiness (optymalizacja dla SSD)
echo "Optymalizacja systemu..."
echo "vm.swappiness=10" >> /etc/sysctl.conf

# Konfiguracja limitów systemowych
echo "Konfiguracja limitów systemowych..."
cat >> /etc/security/limits.conf << 'EOF'
# ARGUS Controller limits
* soft nofile 65536
* hard nofile 65536
* soft nproc 32768
* hard nproc 32768
EOF

# Instalacja snap (jeśli nie jest zainstalowany)
echo "Sprawdzanie snap..."
if ! command -v snap &> /dev/null; then
    echo "Instalacja snap..."
    apt install -y snapd
    systemctl enable snapd
    systemctl start snapd
else
    echo "Snap już zainstalowany"
fi

# Czyszczenie cache apt
echo "Czyszczenie cache..."
apt autoremove -y
apt autoclean

echo "=== Podstawowa konfiguracja Ubuntu 24.04 zakończona ==="
echo "Zainstalowane komponenty:"
echo "- Podstawowe narzędzia systemowe"
echo "- Narzędzia deweloperskie (Python, Node.js, Java)"
echo "- Biblioteki multimedialne i matematyczne"
echo "- Narzędzia sieciowe i SSH"
echo "- Podstawowa konfiguracja firewall"
echo ""
echo "System jest gotowy do instalacji ROS2 i aplikacji kontrolera."
