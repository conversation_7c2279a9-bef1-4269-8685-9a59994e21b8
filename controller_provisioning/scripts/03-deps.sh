#!/bin/bash
# 03-deps.sh - Instalacja zależności dla kontrolera ARGUS na Ubuntu 24.04

set -e

echo "=== Instalacja zależności kontrolera ARGUS ==="

# Aktualizacja listy pakietów
echo "Aktualizacja listy pakietów..."
apt update

# Instalacja Python packages przez apt (zalecane dla Ubuntu)
echo "Instalacja pakietów Python przez apt..."
apt install -y \
    python3-pip \
    python3-venv \
    python3-dev \
    python3-setuptools \
    python3-wheel \
    python3-numpy \
    python3-scipy \
    python3-matplotlib \
    python3-opencv \
    python3-pil \
    python3-requests \
    python3-flask \
    python3-fastapi \
    python3-uvicorn \
    python3-websockets \
    python3-sqlalchemy \
    python3-psycopg2 \
    python3-pymongo \
    python3-redis \
    python3-yaml \
    python3-toml \
    python3-click \
    python3-rich \
    python3-tqdm \
    python3-pytest \
    python3-pytest-cov


# Instalacja Node.js i npm packages
echo "Instalacja Node.js i narzędzi web..."
apt install -y nodejs npm

# Instalacja globalnych pakietów npm
echo "Instalacja globalnych pakietów npm..."
npm install -g \
    typescript \
    @angular/cli \
    @vue/cli \
    create-react-app \
    pm2 \
    nodemon \
    http-server

# Instalacja baz danych
echo "Instalacja baz danych..."
apt install -y \
    sqlite3 \
    libsqlite3-dev \
    postgresql-client \

# Instalacja narzędzi sieciowych i monitoringu
echo "Instalacja narzędzi sieciowych..."
apt install -y \
    mosquitto \
    mosquitto-clients \
    nginx \
    apache2-utils \
    tcpdump \
    wireshark-common \
    iperf3 \
    nload \
    iftop \
    nethogs

# Instalacja narzędzi deweloperskich (headless)
echo "Instalacja narzędzi deweloperskich (headless)..."
apt install -y \
    git-lfs \
    docker.io \
    docker-compose

# Konfiguracja Docker
echo "Konfiguracja Docker..."
systemctl enable docker
systemctl start docker

# Dodanie użytkownika do grupy docker
CURRENT_USER=${SUDO_USER:-$USER}
if [ "$CURRENT_USER" != "root" ]; then
    usermod -aG docker $CURRENT_USER
    echo "Użytkownik $CURRENT_USER dodany do grupy docker"
fi

# Biblioteki dla aplikacji headless (bez GUI)
echo "Instalacja bibliotek dla aplikacji headless..."
apt install -y \
    libasound2-dev \
    libnss3-dev \
    libfontconfig1-dev \
    libfreetype6-dev

# Instalacja bibliotek do przetwarzania obrazu i wideo (headless)
echo "Instalacja bibliotek multimedialnych (headless)..."
apt install -y \
    ffmpeg \
    libavcodec-dev \
    libavformat-dev \
    libavutil-dev \
    libswscale-dev \
    libgstreamer1.0-dev \
    libgstreamer-plugins-base1.0-dev \
    gstreamer1.0-plugins-good \
    gstreamer1.0-libav

# Instalacja narzędzi do komunikacji szeregowej
echo "Instalacja narzędzi komunikacji szeregowej..."
apt install -y \
    minicom \
    screen \
    picocom \
    setserial \
    python3-serial

# Instalacja bibliotek matematycznych i naukowych
echo "Instalacja bibliotek matematycznych..."
apt install -y \
    libopenblas-dev \
    liblapack-dev \
    libatlas-base-dev \
    libeigen3-dev \
    libgsl-dev \
    libfftw3-dev

# Konfiguracja systemd dla aplikacji kontrolera
echo "Konfiguracja systemd..."
systemctl daemon-reload

# Tworzenie wirtualnego środowiska Python (w /root jak robot_provisioning)
echo "Tworzenie wirtualnego środowiska Python..."
if [ ! -d "/root/argus_controller/venv" ]; then
    python3 -m venv "/root/argus_controller/venv"
    echo "Wirtualne środowisko utworzone w: /root/argus_controller/venv"

    # Aktywacja i instalacja podstawowych pakietów w venv
    bash -c "
        source /root/argus_controller/venv/bin/activate
        pip install --upgrade pip setuptools wheel
        pip install \
            fastapi \
            uvicorn[standard] \
            websockets \
            python-socketio \
            aiofiles \
            sqlalchemy \
            alembic \
            pydantic \
            python-jose[cryptography] \
            passlib[bcrypt] \
            python-multipart \
            jinja2 \
            python-dotenv \
            rich \
            typer \
            asyncio-mqtt
    "
else
    echo "Wirtualne środowisko już istnieje - pomijam"
fi

# Konfiguracja uprawnień (wszystko w /root)
echo "Konfiguracja uprawnień..."
chown -R root:root "/root/argus_controller" 2>/dev/null || true

# Czyszczenie cache
echo "Czyszczenie cache..."
apt autoremove -y
apt autoclean
npm cache clean --force 2>/dev/null || true

echo "=== Instalacja zależności zakończona ==="
echo "Zainstalowane komponenty:"
echo "- Python packages (apt + pip)"
echo "- Node.js i npm packages"
echo "- Bazy danych (SQLite, PostgreSQL client, Redis, MongoDB client)"
echo "- Narzędzia sieciowe (MQTT, nginx, monitoring)"
echo "- Narzędzia deweloperskie (Docker, Git)"
echo "- Biblioteki multimedialne (headless)"
echo "- Narzędzia komunikacji szeregowej"
echo "- Biblioteki matematyczne"
echo "- Wirtualne środowisko Python"
echo ""
echo "UWAGA: Docker skonfigurowany dla użytkownika root."
echo "Aby używać Docker jako inny użytkownik, dodaj go do grupy docker: usermod -aG docker username"
