#!/bin/bash
# 02-env.sh - Konfiguracja środowiska kontrolera ARGUS

set -e

echo "=== Konfiguracja środowiska kontrolera ARGUS ==="

# Określenie użytkownika (może być root lub zwykły użytkownik)
CURRENT_USER=${SUDO_USER:-$USER}
USER_HOME=$(eval echo ~$CURRENT_USER)

echo "Konfiguracja dla użytkownika: $CURRENT_USER"
echo "Katalog domowy: $USER_HOME"

# Tworzenie katalogów roboczych (w /root jak w robot_provisioning)
echo "Tworzenie katalogów roboczych..."
mkdir -p /root/argus_controller_ws/src                 # ROS2 workspace (główny)
mkdir -p /root/argus_controller/src                    # Kod źródłowy z GitLab
mkdir -p /root/argus_controller/logs                   # Logi aplikacji
mkdir -p /root/argus_controller/config                 # Konfiguracje
mkdir -p /root/argus_controller/data                   # Dane aplikacji
mkdir -p /root/argus_controller/backups                # Ko<PERSON> zapasowe
mkdir -p /opt/argus_controller                          # Katalog systemowy
mkdir -p /var/log/argus_controller                      # Logi systemowe

# Konfiguracja zmiennych środowiskowych w .bashrc
echo "Konfiguracja zmiennych środowiskowych..."
BASHRC_FILE="$USER_HOME/.bashrc"

# Sprawdź czy konfiguracja już istnieje
if ! grep -q "ARGUS Controller Environment Variables" "$BASHRC_FILE"; then
    cat >> "$BASHRC_FILE" << EOF

# ARGUS Controller Environment Variables (w /root jak robot_provisioning)
export ARGUS_CONTROLLER_ROOT="/root/argus_controller"
export ARGUS_CONTROLLER_SRC="\$ARGUS_CONTROLLER_ROOT/src"
export ARGUS_CONTROLLER_WS="/root/argus_controller_ws"
export ARGUS_CONTROLLER_CONFIG="\$ARGUS_CONTROLLER_ROOT/config"
export ARGUS_CONTROLLER_LOGS="\$ARGUS_CONTROLLER_ROOT/logs"
export ARGUS_CONTROLLER_DATA="\$ARGUS_CONTROLLER_ROOT/data"

# ROS2 Environment (będzie dodane po instalacji ROS2)
# source /opt/ros/jazzy/setup.bash
# source \$ARGUS_CONTROLLER_WS/install/setup.bash

# ROS2 Network Configuration dla kontrolera
export ROS_DOMAIN_ID=42
export ROS_LOCALHOST_ONLY=0

# Python path dla ARGUS Controller
export PYTHONPATH="\$ARGUS_CONTROLLER_SRC:\$ARGUS_CONTROLLER_WS/src:\$PYTHONPATH"

# Node.js path (dla aplikacji web)
export PATH="\$PATH:/usr/local/bin"

# Aliasy dla ARGUS Controller
alias controller-build='cd \$ARGUS_CONTROLLER_WS && colcon build'
alias controller-source='source \$ARGUS_CONTROLLER_WS/install/setup.bash'
alias controller-logs='cd \$ARGUS_CONTROLLER_LOGS'
alias controller-ws='cd \$ARGUS_CONTROLLER_WS'
alias controller-src='cd \$ARGUS_CONTROLLER_SRC'
alias controller-config='cd \$ARGUS_CONTROLLER_CONFIG'
alias controller-data='cd \$ARGUS_CONTROLLER_DATA'

# ROS2 aliasy
alias ros2-nodes='ros2 node list'
alias ros2-topics='ros2 topic list'
alias ros2-services='ros2 service list'
alias ros2-info='ros2 node info'

EOF
else
    echo "Konfiguracja zmiennych środowiskowych już istnieje - pomijam"
fi

# Konfiguracja dla użytkownika argusik (jeśli istnieje)
if id "argusik" &>/dev/null && [ "$CURRENT_USER" != "argusik" ]; then
    echo "Konfiguracja aliasów zarządzania dla użytkownika argusik..."
    ARGUSIK_BASHRC="/home/<USER>/.bashrc"
    
    if [ -f "$ARGUSIK_BASHRC" ] && ! grep -q "ARGUS Controller Management Aliases" "$ARGUSIK_BASHRC"; then
        cat >> "$ARGUSIK_BASHRC" << 'EOF'

# ARGUS Controller Management Aliases
alias controller-status='sudo systemctl status controller_core'
alias controller-restart='sudo systemctl restart controller_core'
alias controller-stop='sudo systemctl stop controller_core'
alias controller-start='sudo systemctl start controller_core'
alias controller-logs='sudo journalctl -u controller_core -f'
alias controller-diag='sudo /opt/argus_controller/bin/controller_diagnostics.sh'

# ROS2 Management
alias ros2-kill='sudo pkill -f ros2'
alias ros2-clean='ros2 daemon stop && ros2 daemon start'

EOF
        chown argusik:argusik "$ARGUSIK_BASHRC"
    else
        echo "Aliasy zarządzania już istnieją - pomijam"
    fi
fi

# Konfiguracja locale
echo "Konfiguracja locale..."
locale-gen en_US.UTF-8
update-locale LANG=en_US.UTF-8

# Konfiguracja timezone
echo "Konfiguracja timezone..."
timedatectl set-timezone Europe/Warsaw

# Konfiguracja hostname (jeśli nie jest ustawiony)
CURRENT_HOSTNAME=$(hostname)
if [[ "$CURRENT_HOSTNAME" == "ubuntu" ]] || [[ "$CURRENT_HOSTNAME" == "localhost" ]]; then
    echo "Konfiguracja hostname..."
    hostnamectl set-hostname argus-controller
    
    # Aktualizacja /etc/hosts
    if ! grep -q "argus-controller" /etc/hosts; then
        echo "*********    argus-controller" >> /etc/hosts
    fi
else
    echo "Hostname już skonfigurowany: $CURRENT_HOSTNAME"
fi

# Tworzenie pliku konfiguracyjnego kontrolera
echo "Tworzenie pliku konfiguracyjnego kontrolera..."
cat > "/root/argus_controller/config/controller.conf" << 'EOF'
# ARGUS Controller Configuration

[controller]
name = argus-controller
version = 1.0.0
debug = false

[ros2]
domain_id = 42
localhost_only = false
discovery_timeout = 30

[network]
# Porty dla komunikacji
api_port = 8080
websocket_port = 8081
ros2_port_range = 7400-7500

[logging]
level = INFO
max_file_size = 10MB
max_files = 5
log_to_console = true

[robots]
# Maksymalna liczba obsługiwanych robotów
max_robots = 10
connection_timeout = 30
heartbeat_interval = 5

[database]
# Konfiguracja bazy danych (SQLite domyślnie)
type = sqlite
path = ../data/controller.db
backup_interval = 3600

[web_interface]
enabled = true
port = 3000
static_files = ../web/dist
EOF

# Ustawienie właściciela katalogów (wszystko w /root)
echo "Ustawienie właściciela katalogów..."
chown -R root:root "/root/argus_controller"
chown -R root:root "/root/argus_controller_ws"
chown -R root:root /var/log/argus_controller

# Tworzenie skryptu aktywacji środowiska
echo "Tworzenie skryptu aktywacji środowiska..."
cat > /opt/argus_controller/setup_env.sh << EOF
#!/bin/bash
# Skrypt aktywacji środowiska ARGUS Controller

# ROS2 Environment
if [ -f "/opt/ros/jazzy/setup.bash" ]; then
    source /opt/ros/jazzy/setup.bash
fi

# Controller workspace (w /root jak robot_provisioning)
if [ -f "/root/argus_controller_ws/install/setup.bash" ]; then
    source /root/argus_controller_ws/install/setup.bash
fi

# Environment variables
export ARGUS_CONTROLLER_ROOT="/root/argus_controller"
export ARGUS_CONTROLLER_SRC="\$ARGUS_CONTROLLER_ROOT/src"
export ARGUS_CONTROLLER_WS="/root/argus_controller_ws"
export ARGUS_CONTROLLER_CONFIG="\$ARGUS_CONTROLLER_ROOT/config"
export ARGUS_CONTROLLER_LOGS="\$ARGUS_CONTROLLER_ROOT/logs"
export ARGUS_CONTROLLER_DATA="\$ARGUS_CONTROLLER_ROOT/data"

export ROS_DOMAIN_ID=42
export ROS_LOCALHOST_ONLY=0

export PYTHONPATH="\$ARGUS_CONTROLLER_SRC:\$ARGUS_CONTROLLER_WS/src:\$PYTHONPATH"

echo "ARGUS Controller environment activated"
echo "Controller root: \$ARGUS_CONTROLLER_ROOT"
echo "Controller workspace: \$ARGUS_CONTROLLER_WS"
echo "ROS2 Domain ID: \$ROS_DOMAIN_ID"
EOF

chmod +x /opt/argus_controller/setup_env.sh

echo "=== Konfiguracja środowiska kontrolera zakończona ==="
echo "Utworzone katalogi:"
echo "- /root/argus_controller/ (główny katalog kontrolera)"
echo "- /root/argus_controller_ws/ (workspace ROS2)"
echo "- /opt/argus_controller/ (katalog systemowy)"
echo "- /var/log/argus_controller/ (logi systemowe)"
echo ""
echo "Aby aktywować środowisko w nowej sesji:"
echo "  source /opt/argus_controller/setup_env.sh"
echo ""
echo "Lub uruchom nowy terminal - środowisko zostanie automatycznie aktywowane."
