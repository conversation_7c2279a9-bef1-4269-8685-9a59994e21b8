#!/bin/bash
# cleanup.sh - Skrypt czyszczący dla kontrolera ARGUS

set -e

echo "=== Czyszczenie systemu kontrolera ARGUS ==="

# Kolory
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Sprawdzenie uprawnień root
if [ "$EUID" -ne 0 ]; then
    log_error "Ten skrypt musi być uruchomiony jako root (sudo)"
    exit 1
fi

CURRENT_USER=${SUDO_USER:-$USER}
USER_HOME=$(eval echo ~$CURRENT_USER)

# Funkcja czyszczenia workspace
cleanup_workspace() {
    log_info "Czyszczenie workspace ROS2..."

    if [ -d "/root/argus_controller_ws" ]; then
        cd "/root/argus_controller_ws"

        # Usuń katalogi build
        rm -rf build/ install/ log/

        # Wyczyść cache colcon
        rm -rf .colcon_install_layout

        log_success "Workspace wyczyszczony"
    else
        log_warning "Workspace nie znaleziony"
    fi
}

# Funkcja czyszczenia logów
cleanup_logs() {
    log_info "Czyszczenie logów..."
    
    # Logi systemowe
    if [ -d "/var/log/argus_controller" ]; then
        find /var/log/argus_controller -name "*.log" -type f -delete
        log_success "Logi systemowe wyczyszczone"
    fi
    
    # Logi kontrolera
    if [ -d "/root/argus_controller/logs" ]; then
        find "/root/argus_controller/logs" -name "*.log" -type f -delete
        log_success "Logi kontrolera wyczyszczone"
    fi

    # Logi ROS2
    if [ -d "/root/.ros/log" ]; then
        rm -rf "/root/.ros/log"/*
        log_success "Logi ROS2 wyczyszczone"
    fi
    
    # Wyczyść journald logs dla controller_core
    journalctl --vacuum-time=1d --unit=controller_core 2>/dev/null || true
}

# Funkcja czyszczenia cache
cleanup_cache() {
    log_info "Czyszczenie cache..."
    
    # APT cache
    apt autoremove -y
    apt autoclean
    
    # Snap cache
    if command -v snap &> /dev/null; then
        snap list --all | awk '/disabled/{print $1, $3}' | while read snapname revision; do
            snap remove "$snapname" --revision="$revision" 2>/dev/null || true
        done
    fi
    
    # Python cache
    find /home -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    find /root -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    
    # Node.js cache
    if command -v npm &> /dev/null; then
        npm cache clean --force 2>/dev/null || true
    fi
    
    # Docker cache (jeśli Docker jest zainstalowany)
    if command -v docker &> /dev/null; then
        docker system prune -f 2>/dev/null || true
    fi
    
    log_success "Cache wyczyszczony"
}

# Funkcja czyszczenia tymczasowych plików
cleanup_temp() {
    log_info "Czyszczenie plików tymczasowych..."
    
    # Pliki tymczasowe systemu
    find /tmp -type f -atime +7 -delete 2>/dev/null || true
    find /var/tmp -type f -atime +7 -delete 2>/dev/null || true
    
    # Pliki tymczasowe ROS2
    find /tmp -name "ros2_*" -type d -exec rm -rf {} + 2>/dev/null || true
    
    # Pliki lock
    find /var/lock -name "*.lock" -type f -delete 2>/dev/null || true
    
    log_success "Pliki tymczasowe wyczyszczone"
}

# Funkcja resetowania konfiguracji
reset_config() {
    log_warning "Resetowanie konfiguracji do domyślnych wartości..."
    
    read -p "Czy na pewno chcesz zresetować konfigurację? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Reset konfiguracji anulowany"
        return 0
    fi
    
    # Backup obecnej konfiguracji
    if [ -f "/root/argus_controller/config/controller.conf" ]; then
        cp "/root/argus_controller/config/controller.conf" \
           "/root/argus_controller/config/controller.conf.backup.$(date +%Y%m%d_%H%M%S)"
        log_info "Kopia zapasowa konfiguracji utworzona"
    fi

    # Przywróć domyślną konfigurację
    if [ -f "/opt/argus_controller/config/controller.conf.default" ]; then
        cp "/opt/argus_controller/config/controller.conf.default" \
           "/root/argus_controller/config/controller.conf"
        log_success "Konfiguracja zresetowana do domyślnych wartości"
    else
        log_warning "Domyślna konfiguracja nie znaleziona"
    fi
}

# Funkcja pełnego czyszczenia (UWAGA: usuwa wszystkie dane!)
full_cleanup() {
    log_error "UWAGA: Pełne czyszczenie usunie WSZYSTKIE dane kontrolera!"
    log_error "To działanie jest nieodwracalne!"
    echo ""
    read -p "Czy na pewno chcesz kontynuować? Wpisz 'DELETE' aby potwierdzić: " confirm
    
    if [ "$confirm" != "DELETE" ]; then
        log_info "Pełne czyszczenie anulowane"
        return 0
    fi
    
    log_warning "Zatrzymywanie usług..."
    systemctl stop controller_core 2>/dev/null || true
    
    log_warning "Usuwanie katalogów kontrolera..."
    rm -rf "/root/argus_controller"
    rm -rf "/root/argus_controller_ws"
    rm -rf "/opt/argus_controller"
    rm -rf "/var/log/argus_controller"
    
    log_warning "Usuwanie usług systemowych..."
    systemctl disable controller_core 2>/dev/null || true
    rm -f /etc/systemd/system/controller_core.service
    systemctl daemon-reload
    
    log_warning "Usuwanie konfiguracji sieciowej..."
    rm -f /etc/avahi/services/argus-controller.service
    rm -f /etc/netplan/99-argus-controller.yaml
    
    log_success "Pełne czyszczenie zakończone"
    log_info "System wymaga restartu: sudo reboot"
}

# Funkcja wyświetlania pomocy
show_help() {
    echo "ARGUS Controller Cleanup Script"
    echo ""
    echo "Użycie:"
    echo "  sudo $0 [opcje]"
    echo ""
    echo "Opcje:"
    echo "  -w, --workspace     Wyczyść tylko workspace ROS2"
    echo "  -l, --logs          Wyczyść tylko logi"
    echo "  -c, --cache         Wyczyść tylko cache"
    echo "  -t, --temp          Wyczyść tylko pliki tymczasowe"
    echo "  -r, --reset-config  Zresetuj konfigurację do domyślnych wartości"
    echo "  -a, --all           Wyczyść wszystko (workspace, logi, cache, temp)"
    echo "  -f, --full          PEŁNE CZYSZCZENIE - usuwa wszystkie dane!"
    echo "  -h, --help          Wyświetl tę pomoc"
    echo ""
    echo "Przykłady:"
    echo "  sudo $0 --workspace    # Wyczyść workspace"
    echo "  sudo $0 --logs         # Wyczyść logi"
    echo "  sudo $0 --all          # Standardowe czyszczenie"
    echo "  sudo $0 --full         # UWAGA: Usuwa wszystko!"
    echo ""
}

# Funkcja główna
main() {
    if [ $# -eq 0 ]; then
        show_help
        exit 0
    fi
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -w|--workspace)
                cleanup_workspace
                shift
                ;;
            -l|--logs)
                cleanup_logs
                shift
                ;;
            -c|--cache)
                cleanup_cache
                shift
                ;;
            -t|--temp)
                cleanup_temp
                shift
                ;;
            -r|--reset-config)
                reset_config
                shift
                ;;
            -a|--all)
                cleanup_workspace
                cleanup_logs
                cleanup_cache
                cleanup_temp
                shift
                ;;
            -f|--full)
                full_cleanup
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "Nieznana opcja: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_success "Czyszczenie zakończone"
}

main "$@"
