#!/bin/bash
# 04-ros2.sh - Instalacja ROS2 Jazzy z natywnych pakietów Ubuntu 24.04

set -e

echo "=== Instalacja ROS2 Jazzy na Ubuntu 24.04 ==="

# Sprawdzenie wersji Ubuntu
. /etc/os-release
echo "Wykryto system: $PRETTY_NAME"

if [[ "$VERSION_ID" != "24.04" ]]; then
    echo "UWAGA: ROS2 Jazzy jest natywnie dostępny w Ubuntu 24.04"
    echo "Obecna wersja: $PRETTY_NAME"
    echo "Instalacja może nie działać poprawnie na innych wersjach"
fi

# Aktualizacja systemu
echo "Aktualizacja systemu..."
apt update
apt upgrade -y

# Instalacja narzędzi wymaganych do dodania repozytoriów
echo "Instalacja narzędzi do zarządzania repozytoriami..."
apt install -y \
    software-properties-common \
    curl \
    gnupg \
    lsb-release

# Dodanie klucza GPG ROS2
echo "Dodawanie klucza GPG ROS2..."
curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key -o /usr/share/keyrings/ros-archive-keyring.gpg

# Dodanie repozytorium ROS2
echo "Dodawanie repozytorium ROS2..."
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] http://packages.ros.org/ros2/ubuntu $(. /etc/os-release && echo $UBUNTU_CODENAME) main" | tee /etc/apt/sources.list.d/ros2.list > /dev/null

# Aktualizacja listy pakietów
echo "Aktualizacja listy pakietów..."
apt update

# Instalacja ROS2 Jazzy Base (headless - bez GUI)
echo "Instalacja ROS2 Jazzy Base (headless)..."
apt install -y ros-jazzy-ros-base

# Instalacja dodatkowych pakietów ROS2 (headless)
echo "Instalacja dodatkowych pakietów ROS2 (headless)..."
apt install -y \
    ros-jazzy-example-interfaces \
    ros-jazzy-demo-nodes-cpp \
    ros-jazzy-demo-nodes-py \
    ros-jazzy-navigation2 \
    ros-jazzy-nav2-bringup \
    ros-jazzy-slam-toolbox \
    ros-jazzy-robot-localization \
    ros-jazzy-tf2-tools \
    ros-jazzy-joint-state-publisher \
    ros-jazzy-robot-state-publisher \
    ros-jazzy-xacro

# Instalacja narzędzi deweloperskich ROS2
echo "Instalacja narzędzi deweloperskich ROS2..."
apt install -y \
    python3-colcon-common-extensions \
    python3-colcon-mixin \
    python3-rosdep \
    python3-vcstool \
    python3-argcomplete \
    ros-dev-tools

# Instalacja pakietów do komunikacji
echo "Instalacja pakietów komunikacyjnych..."
apt install -y \
    ros-jazzy-rmw-fastrtps-cpp \
    ros-jazzy-rosbridge-suite \
    ros-jazzy-web-video-server \
    ros-jazzy-compressed-image-transport \
    ros-jazzy-image-transport-plugins

# Instalacja pakietów do pracy z sensorami
echo "Instalacja pakietów sensorowych..."
apt install -y \
    ros-jazzy-sensor-msgs \
    ros-jazzy-geometry-msgs \
    ros-jazzy-std-msgs \
    ros-jazzy-nav-msgs \
    ros-jazzy-visualization-msgs \
    ros-jazzy-diagnostic-msgs \
    ros-jazzy-cv-bridge \
    ros-jazzy-image-geometry

# Instalacja pakietów do kontroli robotów
echo "Instalacja pakietów kontroli robotów..."
apt install -y \
    ros-jazzy-control-msgs \
    ros-jazzy-controller-manager \
    ros-jazzy-joint-state-broadcaster \
    ros-jazzy-diff-drive-controller \
    ros-jazzy-velocity-controllers \
    ros-jazzy-position-controllers

# Inicjalizacja rosdep
echo "Inicjalizacja rosdep..."
if [ ! -f /etc/ros/rosdep/sources.list.d/20-default.list ]; then
    rosdep init
else
    echo "rosdep już zainicjalizowany - aktualizacja..."
fi

# Aktualizacja rosdep dla wszystkich użytkowników
rosdep update

# Konfiguracja środowiska ROS2 dla root
echo "Konfiguracja środowiska ROS2 dla root..."
if ! grep -q "ROS2 Jazzy Environment" /root/.bashrc; then
    cat >> /root/.bashrc << 'EOF'

# ROS2 Jazzy Environment (Ubuntu 24.04 native)
source /opt/ros/jazzy/setup.bash

# ROS2 Domain ID dla ARGUS Controller
export ROS_DOMAIN_ID=42

# ROS2 Network communication enabled for controller-robot connection
export ROS_LOCALHOST_ONLY=0

# ROS2 Middleware (FastRTPS domyślnie)
export RMW_IMPLEMENTATION=rmw_fastrtps_cpp

# Colcon completion
source /usr/share/colcon_argcomplete/hook/colcon-argcomplete.bash

EOF
else
    echo "Konfiguracja ROS2 już istnieje w .bashrc root - pomijam"
fi

# Konfiguracja środowiska ROS2 dla użytkownika
CURRENT_USER=${SUDO_USER:-$USER}
USER_HOME=$(eval echo ~$CURRENT_USER)

if [ "$CURRENT_USER" != "root" ] && [ -d "$USER_HOME" ]; then
    echo "Konfiguracja środowiska ROS2 dla użytkownika $CURRENT_USER..."
    USER_BASHRC="$USER_HOME/.bashrc"
    
    if ! grep -q "ROS2 Jazzy Environment" "$USER_BASHRC"; then
        cat >> "$USER_BASHRC" << 'EOF'

# ROS2 Jazzy Environment (Ubuntu 24.04 native)
source /opt/ros/jazzy/setup.bash

# ARGUS Controller workspace
if [ -f "/root/argus_controller_ws/install/setup.bash" ]; then
    source /root/argus_controller_ws/install/setup.bash
fi

# ROS2 Domain ID dla ARGUS Controller
export ROS_DOMAIN_ID=42

# ROS2 Network communication enabled for controller-robot connection
export ROS_LOCALHOST_ONLY=0

# ROS2 Middleware (FastRTPS domyślnie)
export RMW_IMPLEMENTATION=rmw_fastrtps_cpp

# Colcon completion
source /usr/share/colcon_argcomplete/hook/colcon-argcomplete.bash

EOF
        chown $CURRENT_USER:$CURRENT_USER "$USER_BASHRC"
    else
        echo "Konfiguracja ROS2 już istnieje w .bashrc użytkownika - pomijam"
    fi
fi

# Tworzenie skryptu aktywacji ROS2
echo "Tworzenie skryptu aktywacji ROS2..."
cat > /opt/argus_controller/ros2_setup.sh << 'EOF'
#!/bin/bash
# Skrypt aktywacji ROS2 Jazzy dla ARGUS Controller

# ROS2 Environment
source /opt/ros/jazzy/setup.bash

# Controller workspace (jeśli istnieje)
if [ -f "/root/argus_controller_ws/install/setup.bash" ]; then
    source /root/argus_controller_ws/install/setup.bash
fi

# ROS2 Configuration
export ROS_DOMAIN_ID=42
export ROS_LOCALHOST_ONLY=0
export RMW_IMPLEMENTATION=rmw_fastrtps_cpp

echo "ROS2 Jazzy environment activated for ARGUS Controller"
echo "ROS_DISTRO: $ROS_DISTRO"
echo "ROS_DOMAIN_ID: $ROS_DOMAIN_ID"
echo "RMW_IMPLEMENTATION: $RMW_IMPLEMENTATION"
EOF

chmod +x /opt/argus_controller/ros2_setup.sh

# Test instalacji ROS2
echo "Test instalacji ROS2..."
source /opt/ros/jazzy/setup.bash
export ROS_DOMAIN_ID=42
export ROS_LOCALHOST_ONLY=0

# Sprawdzenie czy ROS2 działa
if command -v ros2 &> /dev/null; then
    echo "✓ ROS2 zainstalowany pomyślnie"
    echo "Wersja ROS2: $(ros2 --version)"
    echo "Dostępne pakiety: $(ros2 pkg list | wc -l)"
else
    echo "✗ Problem z instalacją ROS2"
    exit 1
fi

# Konfiguracja colcon mixins
echo "Konfiguracja colcon mixins..."
colcon mixin add default https://raw.githubusercontent.com/colcon/colcon-mixin-repository/master/index.yaml || true
colcon mixin update default || true

echo "=== Instalacja ROS2 Jazzy zakończona ==="
echo ""
echo "ROS2 Jazzy został zainstalowany z natywnych pakietów Ubuntu 24.04"
echo ""
echo "Aby aktywować ROS2 w nowej sesji:"
echo "  source /opt/argus_controller/ros2_setup.sh"
echo ""
echo "Lub uruchom nowy terminal - ROS2 zostanie automatycznie aktywowany."
echo ""
echo "Aby przetestować instalację, uruchom w dwóch terminalach:"
echo "  Terminal 1: ros2 run demo_nodes_cpp talker"
echo "  Terminal 2: ros2 run demo_nodes_py listener"
echo ""
echo "Dostępne narzędzia (headless):"
echo "- colcon (budowanie workspace)"
echo "- ros2 cli (narzędzia wiersza poleceń)"
echo "- navigation2 (nawigacja robotów)"
echo "- slam-toolbox (mapowanie)"
