#!/bin/bash
# 06-services.sh - Konfiguracja usług systemowych kontrolera ARGUS

set -e

echo "=== Konfiguracja usług systemowych kontrolera ==="

# Określenie użytkownika (workspace w /root jak robot_provisioning)
CURRENT_USER=${SUDO_USER:-$USER}
USER_HOME=$(eval echo ~$CURRENT_USER)
CONTROLLER_ROOT="/root/argus_controller"
CONTROLLER_WS="/root/argus_controller_ws"

# Tworzenie katalogów dla logów i skryptów
echo "Tworzenie katalogów systemowych..."
mkdir -p /var/log/argus_controller
mkdir -p /opt/argus_controller/bin
mkdir -p /opt/argus_controller/config
mkdir -p /opt/argus_controller/scripts

# Określenie ścieżki do katalogu provisioning
PROVISION_DIR="$(dirname "$(dirname "$(realpath "$0")")")"

# Kopiowanie i instalacja usługi systemowej
echo "Instalacja usługi systemowej controller_core..."
if [ -f "$PROVISION_DIR/services/controller_core.service" ]; then
    cp "$PROVISION_DIR/services/controller_core.service" /etc/systemd/system/
    systemctl daemon-reload
    systemctl enable controller_core.service
    echo "✓ Usługa controller_core zainstalowana i włączona"
else
    echo "⚠ Plik usługi controller_core.service nie znaleziony - tworzę domyślny"
    
    # Tworzenie domyślnej usługi
    cat > /etc/systemd/system/controller_core.service << EOF
[Unit]
Description=ARGUS Controller Core Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/root/argus_controller_ws
Environment=HOME=/root
Environment=USER=root
ExecStartPre=/bin/bash -c 'source /opt/ros/jazzy/setup.bash && source /root/argus_controller_ws/install/setup.bash'
ExecStart=/bin/bash -c 'source /opt/ros/jazzy/setup.bash && source /root/argus_controller_ws/install/setup.bash && ros2 launch controller_core controller.launch.py'
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    systemctl daemon-reload
    systemctl enable controller_core.service
fi

# Pobieranie kodu źródłowego controller_core z GitLab
echo "Pobieranie kodu źródłowego controller_core z GitLab..."
cd "/root/argus_controller/src"

# Sprawdzenie czy git jest dostępny
if ! command -v git &> /dev/null; then
    echo "BŁĄD: Git nie jest zainstalowany!"
    exit 1
fi

# Wczytanie konfiguracji GitLab
GITLAB_CONFIG="$PROVISION_DIR/config/gitlab.conf"
if [ -f "$GITLAB_CONFIG" ]; then
    echo "Wczytywanie konfiguracji GitLab z $GITLAB_CONFIG"
    source "$GITLAB_CONFIG"
else
    echo "UWAGA: Nie znaleziono pliku konfiguracyjnego GitLab, używam domyślnych wartości"
    CONTROLLER_CORE_REPO_URL="https://gitlab.devforyou.pl/avotech/controller_core.git"
    CONTROLLER_CORE_BRANCH="master"
    GIT_CLONE_OPTIONS="--single-branch"
fi

echo "Klonowanie repozytorium: $CONTROLLER_CORE_REPO_URL (branch: $CONTROLLER_CORE_BRANCH)"

# Konfiguracja git dla self-hosted GitLab
if [ "${GITLAB_SELF_HOSTED:-false}" = "true" ]; then
    echo "Konfiguracja git dla self-hosted GitLab..."
    
    # SSL verification
    if [ "${GIT_SSL_VERIFY:-true}" = "false" ]; then
        echo "Wyłączanie weryfikacji SSL dla self-hosted GitLab"
        git config --global http.sslVerify false
    fi
    
    # HTTP timeouts
    git config --global http.lowSpeedLimit "${GIT_HTTP_LOW_SPEED_LIMIT:-1000}"
    git config --global http.lowSpeedTime "${GIT_HTTP_LOW_SPEED_TIME:-60}"
    git config --global http.postBuffer 524288000
fi

# Klonowanie z timeout
if timeout "${GIT_TIMEOUT:-300}" git clone $GIT_CLONE_OPTIONS --branch "$CONTROLLER_CORE_BRANCH" "$CONTROLLER_CORE_REPO_URL" controller_core; then
    echo "Pomyślnie pobrano controller_core z GitLab"
    
    # Linkowanie pakietów do workspace ROS2
    echo "Linkowanie pakietów controller_core do workspace ROS2..."
    if [ -d "/root/argus_controller/src/controller_core" ]; then
        # Usuń stare linki jeśli istnieją
        find "/root/argus_controller_ws/src/" -type l -delete 2>/dev/null || true

        # Linkuj każdy pakiet ROS2 osobno
        for package_dir in "/root/argus_controller/src/controller_core/"*/; do
            if [ -f "$package_dir/package.xml" ]; then
                package_name=$(basename "$package_dir")
                echo "Linkowanie pakietu: $package_name"
                ln -sf "$package_dir" "/root/argus_controller_ws/src/$package_name"
            fi
        done

        # Jeśli controller_core ma główny package.xml, linkuj też główny katalog
        if [ -f "/root/argus_controller/src/controller_core/package.xml" ]; then
            echo "Linkowanie głównego pakietu controller_core"
            ln -sf "/root/argus_controller/src/controller_core" "/root/argus_controller_ws/src/controller_core"
        fi
        
        echo "Pakiety controller_core zlinkowane do workspace"
    else
        echo "BŁĄD: Nie znaleziono pobranego katalogu controller_core"
        exit 1
    fi
else
    echo "BŁĄD: Nie udało się pobrać repozytorium controller_core"
    echo "Sprawdź:"
    echo "1. URL repozytorium: $CONTROLLER_CORE_REPO_URL"
    echo "2. Połączenie z self-hosted GitLab"
    echo "3. Dane uwierzytelniające (jeśli repo prywatne)"
    echo "4. Certyfikaty SSL (ustaw GIT_SSL_VERIFY=false jeśli potrzeba)"
    
    # Test połączenia z GitLab
    GITLAB_HOST=$(echo "$CONTROLLER_CORE_REPO_URL" | sed -n 's|https\?://\([^/]*\).*|\1|p')
    if [ -n "$GITLAB_HOST" ]; then
        echo "Testowanie połączenia z $GITLAB_HOST..."
        if ping -c 3 "$GITLAB_HOST" &>/dev/null; then
            echo "✓ Połączenie sieciowe z $GITLAB_HOST działa"
        else
            echo "✗ Brak połączenia sieciowego z $GITLAB_HOST"
        fi
        
        if curl -s --connect-timeout 10 "https://$GITLAB_HOST" &>/dev/null; then
            echo "✓ HTTPS połączenie z $GITLAB_HOST działa"
        else
            echo "✗ Problem z HTTPS połączeniem do $GITLAB_HOST"
        fi
    fi
    
    echo "Kontynuuję bez kodu źródłowego - usługa będzie wymagać ręcznej konfiguracji"
fi

# Budowanie workspace ROS2 (jeśli są pakiety)
echo "Sprawdzanie workspace ROS2..."
cd "/root/argus_controller_ws"

if [ "$(ls -A src/ 2>/dev/null)" ]; then
    echo "Budowanie workspace ROS2..."
    source /opt/ros/jazzy/setup.bash
    
    # Instalacja zależności przez rosdep
    echo "Instalacja zależności ROS2..."
    rosdep update
    rosdep install --from-paths src --ignore-src -r -y || echo "Niektóre zależności mogą być niedostępne"
    
    # Budowanie
    echo "Budowanie pakietów..."
    colcon build --symlink-install --cmake-args -DCMAKE_BUILD_TYPE=Release
    
    echo "✓ Workspace ROS2 zbudowany"
else
    echo "⚠ Brak pakietów w workspace - pomijam budowanie"
fi

# Konfiguracja logowania
echo "Konfiguracja logowania..."
cat > /etc/logrotate.d/argus_controller << 'EOF'
/var/log/argus_controller/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        systemctl reload controller_core 2>/dev/null || true
    endscript
}
EOF

# Tworzenie skryptu diagnostycznego
echo "Tworzenie skryptu diagnostycznego..."
cat > /opt/argus_controller/bin/controller_diagnostics.sh << EOF
#!/bin/bash
# ARGUS Controller Diagnostics Script

echo "=== ARGUS Controller Diagnostics ==="
echo "Date: \$(date)"
echo "Hostname: \$(hostname)"
echo "IP Address: \$(hostname -I | tr -d ' ')"
echo ""

echo "=== System Info ==="
echo "OS: \$(cat /etc/os-release | grep PRETTY_NAME | cut -d'\"' -f2)"
echo "Kernel: \$(uname -r)"
echo "Architecture: \$(uname -m)"
echo "Uptime: \$(uptime -p)"
echo "Load: \$(uptime | awk -F'load average:' '{print \$2}')"
echo "Memory: \$(free -h | grep Mem | awk '{print \$3\"/\"\$2}')"
echo "Disk: \$(df -h / | tail -1 | awk '{print \$3\"/\"\$2\" (\"\$5\" used)\"}')"
echo ""

echo "=== ROS2 Status ==="
if [ -f "/opt/ros/jazzy/setup.bash" ]; then
    source /opt/ros/jazzy/setup.bash 2>/dev/null
    if [ -f "/root/argus_controller_ws/install/setup.bash" ]; then
        source /root/argus_controller_ws/install/setup.bash 2>/dev/null
    fi

    echo "ROS_DISTRO: \${ROS_DISTRO:-not set}"
    echo "ROS_DOMAIN_ID: \${ROS_DOMAIN_ID:-not set}"
    echo "ROS_LOCALHOST_ONLY: \${ROS_LOCALHOST_ONLY:-not set}"
    echo "RMW_IMPLEMENTATION: \${RMW_IMPLEMENTATION:-not set}"
    echo "Controller Workspace: /root/argus_controller_ws"
    
    # Sprawdź czy ROS2 działa
    if command -v ros2 >/dev/null 2>&1; then
        echo "ROS2 Command: Available"
        echo "ROS2 Nodes:"
        timeout 5 ros2 node list 2>/dev/null || echo "  No nodes running or timeout"
        echo "ROS2 Topics:"
        timeout 5 ros2 topic list 2>/dev/null | head -10 || echo "  No topics or timeout"
    else
        echo "ROS2 Command: Not available"
    fi
else
    echo "ROS2 not installed"
fi
echo ""

echo "=== Services Status ==="
services=("controller_core" "NetworkManager" "avahi-daemon" "systemd-resolved")
for service in "\${services[@]}"; do
    if systemctl is-active --quiet "\$service"; then
        echo "\$service: ✓ active"
    else
        echo "\$service: ✗ inactive"
    fi
done
echo ""

echo "=== Network Status ==="
echo "Active connections:"
ip addr show | grep -E "^[0-9]+:|inet " | grep -v "127.0.0.1"
echo ""
echo "Firewall status:"
ufw status numbered 2>/dev/null || echo "UFW not configured"
echo ""
echo "Avahi services:"
avahi-browse -t _argus-controller._tcp 2>/dev/null | head -5 || echo "No Avahi services found"
echo ""

echo "=== Controller Status ==="
echo "Controller directory: /root/argus_controller"
echo "Workspace status:"
if [ -d "/root/argus_controller_ws/install" ]; then
    echo "  ✓ Workspace built"
    echo "  Packages: \$(ls /root/argus_controller_ws/install/ 2>/dev/null | wc -l)"
else
    echo "  ✗ Workspace not built"
fi
echo ""

echo "=== Recent Controller Logs ==="
if [ -d "/var/log/argus_controller" ]; then
    for logfile in /var/log/argus_controller/*.log; do
        if [ -f "\$logfile" ]; then
            echo "--- \$(basename "\$logfile") (last 5 lines) ---"
            tail -n 5 "\$logfile" 2>/dev/null
        fi
    done
else
    echo "No controller log directory found"
fi
echo ""

echo "=== System Logs (last 10 lines) ==="
journalctl -u controller_core --no-pager -n 10 2>/dev/null || echo "No controller_core service logs"
EOF

chmod +x /opt/argus_controller/bin/controller_diagnostics.sh

# Tworzenie skryptu zarządzania kontrolerem
echo "Tworzenie skryptu zarządzania kontrolerem..."
cat > /opt/argus_controller/bin/controller_manager.sh << EOF
#!/bin/bash
# ARGUS Controller Management Script

CONTROLLER_USER="root"
CONTROLLER_HOME="/root/argus_controller"
CONTROLLER_WS="/root/argus_controller_ws"

case "\$1" in
    start)
        echo "Starting ARGUS Controller..."
        systemctl start controller_core
        ;;
    stop)
        echo "Stopping ARGUS Controller..."
        systemctl stop controller_core
        ;;
    restart)
        echo "Restarting ARGUS Controller..."
        systemctl restart controller_core
        ;;
    status)
        echo "ARGUS Controller Status:"
        systemctl status controller_core
        ;;
    logs)
        echo "ARGUS Controller Logs:"
        journalctl -u controller_core -f
        ;;
    build)
        echo "Building controller workspace..."
        cd "\$CONTROLLER_WS"
        source /opt/ros/jazzy/setup.bash
        colcon build --symlink-install
        ;;
    clean)
        echo "Cleaning controller workspace..."
        cd "\$CONTROLLER_WS"
        rm -rf build/ install/ log/
        ;;
    update)
        echo "Updating controller source code..."
        cd "\$CONTROLLER_HOME/src/controller_core"
        git pull
        cd "\$CONTROLLER_WS"
        source /opt/ros/jazzy/setup.bash
        colcon build --symlink-install
        systemctl restart controller_core
        ;;
    diag)
        /opt/argus_controller/bin/controller_diagnostics.sh
        ;;
    *)
        echo "Usage: \$0 {start|stop|restart|status|logs|build|clean|update|diag}"
        echo ""
        echo "Commands:"
        echo "  start   - Start controller service"
        echo "  stop    - Stop controller service"
        echo "  restart - Restart controller service"
        echo "  status  - Show service status"
        echo "  logs    - Show live logs"
        echo "  build   - Build workspace"
        echo "  clean   - Clean workspace"
        echo "  update  - Update source and rebuild"
        echo "  diag    - Run diagnostics"
        exit 1
        ;;
esac
EOF

chmod +x /opt/argus_controller/bin/controller_manager.sh

# Dodanie aliasów dla zarządzania (dla root)
echo "Dodawanie aliasów zarządzania dla root..."
if ! grep -q "alias controller-manage=" /root/.bashrc; then
    cat >> /root/.bashrc << 'EOF'

# ARGUS Controller Management Aliases
alias controller-manage='/opt/argus_controller/bin/controller_manager.sh'
alias controller-diag='/opt/argus_controller/bin/controller_diagnostics.sh'
alias controller-start='systemctl start controller_core'
alias controller-stop='systemctl stop controller_core'
alias controller-restart='systemctl restart controller_core'
alias controller-status='systemctl status controller_core'
alias controller-logs='journalctl -u controller_core -f'
EOF
    echo "✓ Aliasy zarządzania dodane dla root"
else
    echo "ℹ Aliasy zarządzania już istnieją dla root"
fi

# Ustawienie właściciela katalogów
echo "Ustawienie właściciela katalogów..."
chown -R $CURRENT_USER:$CURRENT_USER "$USER_HOME/argus_controller" 2>/dev/null || true
chown -R $CURRENT_USER:$CURRENT_USER /var/log/argus_controller 2>/dev/null || true

# Tworzenie pliku konfiguracyjnego ROS2 dla kontrolera
echo "Tworzenie konfiguracji ROS2..."
cat > /opt/argus_controller/config/ros2.conf << 'EOF'
# ROS2 Configuration for ARGUS Controller

# Domain ID (must match robots)
ROS_DOMAIN_ID=42

# Network settings
ROS_LOCALHOST_ONLY=0

# Middleware
RMW_IMPLEMENTATION=rmw_fastrtps_cpp

# Discovery settings
ROS_DISCOVERY_SERVER=
FASTRTPS_DEFAULT_PROFILES_FILE=

# Logging
RCUTILS_LOGGING_SEVERITY_THRESHOLD=INFO
RCUTILS_COLORIZED_OUTPUT=1

# Performance tuning
RMW_FASTRTPS_USE_QOS_FROM_XML=0
FASTRTPS_DEFAULT_PROFILES_FILE=/opt/argus_controller/config/fastrtps_profile.xml
EOF

# Tworzenie profilu FastRTPS
echo "Tworzenie profilu FastRTPS..."
cat > /opt/argus_controller/config/fastrtps_profile.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8" ?>
<profiles xmlns="http://www.eprosima.com/XMLSchemas/fastRTPS_Profiles">
    <participant profile_name="controller_participant">
        <rtps>
            <name>ARGUSController</name>
            <builtin>
                <discovery_config>
                    <discoveryProtocol>SIMPLE</discoveryProtocol>
                    <use_liveliness_qos>true</use_liveliness_qos>
                </discovery_config>
            </builtin>
        </rtps>
    </participant>
</profiles>
EOF

echo "=== Konfiguracja usług kontrolera zakończona ==="
echo ""
echo "Zainstalowane komponenty:"
echo "- Usługa systemowa controller_core"
echo "- Skrypty zarządzania w /opt/argus_controller/bin/"
echo "- Konfiguracja logowania"
echo "- Aliasy zarządzania"
echo "- Konfiguracja ROS2 i FastRTPS"
echo ""
echo "Dostępne komendy zarządzania:"
echo "  controller-manage start|stop|restart|status|logs|build|clean|update|diag"
echo "  controller-diag (diagnostyka)"
echo ""
echo "UWAGA: Usługa controller_core zostanie uruchomiona po pierwszym budowaniu workspace"
echo "Aby zbudować workspace: cd /root/argus_controller_ws && colcon build"
