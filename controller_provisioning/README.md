# ARGUS Controller Provisioning

Automatyczna instalacja i konfiguracja kontrolera ARGUS na Ubuntu 24.04 LTS z ROS2 Jazzy.

## Wymagania

- **System operacyjny**: Ubuntu 24.04 LTS Server (headless)
- **Hardware**: Raspberry Pi 4/5 (4GB+ RAM) lub PC/Server
- **Architektura**: ARM64 (Raspberry Pi) lub x86_64
- **RAM**: Minimum 4GB (zalecane 8GB dla wielu robotów)
- **Dysk**: Minimum 32GB wolnego miejsca
- **Sieć**: Połączenie internetowe podczas instalacji, LAN dla robotów

## Różnice względem robot_provisioning

| Aspekt | Robot (Raspbian) | Controller (Ubuntu 24.04) |
|--------|------------------|---------------------------|
| System | Raspbian Bookworm | Ubuntu 24.04 LTS |
| ROS2 | Ze źródeł | Natywne pakiety apt |
| Hardware | GPIO, Camera, I2C | Raspberry Pi 4/5 (headless) |
| Rola | Wykonawca | Kontroler/Koordynator |
| Interface | Brak | Web API + SSH |

## Instalacja

### Szybka instalacja
```bash
sudo ./install.sh
```

### Instalacja krok po kroku
```bash
# 1. Podstawowa konfiguracja systemu
sudo ./scripts/01-base.sh

# 2. Konfiguracja środowiska
sudo ./scripts/02-env.sh

# 3. Instalacja zależności
sudo ./scripts/03-deps.sh

# 4. Instalacja ROS2 Jazzy
sudo ./scripts/04-ros2.sh

# 5. Konfiguracja sieci
sudo ./scripts/05-network.sh

# 6. Konfiguracja usług
sudo ./scripts/06-services.sh
```

## Struktura katalogów

```
controller_provisioning/
├── install.sh              # Główny skrypt instalacyjny
├── README.md               # Ta dokumentacja
├── COMMANDS.md             # Przydatne komendy
├── scripts/                # Skrypty instalacyjne
│   ├── 01-base.sh         # Podstawowa konfiguracja Ubuntu
│   ├── 02-env.sh          # Konfiguracja środowiska
│   ├── 03-deps.sh         # Instalacja zależności
│   ├── 04-ros2.sh         # Instalacja ROS2 Jazzy
│   ├── 05-network.sh      # Konfiguracja sieci
│   ├── 06-services.sh     # Konfiguracja usług
│   └── cleanup.sh         # Skrypt czyszczący
├── services/              # Pliki usług systemowych
│   └── controller_core.service
├── config/                # Pliki konfiguracyjne
│   ├── gitlab.conf        # Konfiguracja GitLab
│   └── ros2.conf          # Konfiguracja ROS2
├── files/                 # Dodatkowe pliki
└── network/              # Konfiguracja sieci
    └── netplan/          # Konfiguracje Netplan

Po instalacji struktura w systemie:
/root/argus_controller/     # Główny katalog (jak robot_provisioning)
├── src/                    # Kod źródłowy z GitLab
├── config/                 # Konfiguracje
├── logs/                   # Logi aplikacji
├── data/                   # Dane aplikacji
└── backups/                # Kopie zapasowe

/root/argus_controller_ws/  # Workspace ROS2 (jak robot_provisioning)
├── src/                    # Pakiety ROS2 (linki do controller/src)
├── build/                  # Pliki budowania
├── install/                # Zainstalowane pakiety
└── log/                    # Logi budowania
```

## Konfiguracja po instalacji

### Testowanie ROS2
```bash
# Terminal 1
ros2 run demo_nodes_cpp talker

# Terminal 2  
ros2 run demo_nodes_py listener
```

### Diagnostyka
```bash
# Sprawdzenie statusu systemu
argus-diag

# Status usług
systemctl status controller_core
```

### Zarządzanie usługami
```bash
# Start/stop/restart kontrolera
sudo systemctl start controller_core
sudo systemctl stop controller_core  
sudo systemctl restart controller_core

# Logi
sudo journalctl -u controller_core -f
```

## Rozwiązywanie problemów

### ROS2 nie działa
```bash
# Sprawdź instalację
ros2 --version

# Sprawdź zmienne środowiskowe
echo $ROS_DISTRO
echo $ROS_DOMAIN_ID

# Ponowne źródłowanie
source /opt/ros/jazzy/setup.bash
```

### Problemy sieciowe
```bash
# Sprawdź konfigurację sieci
ip addr show
netplan status

# Test komunikacji ROS2
ros2 node list
ros2 topic list
```

## Kontakt

W przypadku problemów sprawdź logi lub skontaktuj się z zespołem deweloperskim.
