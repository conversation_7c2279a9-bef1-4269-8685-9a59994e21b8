# GitLab Configuration for ARGUS Controller
# Konfiguracja dostępu do repozytoriów GitLab

# Repository URLs
CONTROLLER_CORE_REPO_URL="https://gitlab.devforyou.pl/avotech/controller_core.git"
CONTROLLER_CORE_BRANCH="master"

# Alternative repositories (if main is not available)
CONTROLLER_CORE_REPO_URL_ALT="https://github.com/argus-project/controller_core.git"
CONTROLLER_API_REPO_URL="https://gitlab.devforyou.pl/avotech/controller_api.git"
CONTROLLER_VIEW_REPO_URL="https://gitlab.devforyou.pl/avotech/controller_view.git"

# Git clone options
GIT_CLONE_OPTIONS="--single-branch --depth 1"
GIT_TIMEOUT=300

# Self-hosted GitLab settings
GITLAB_SELF_HOSTED=true
GITLAB_HOST="gitlab.devforyou.pl"

# SSL/TLS settings for self-hosted GitLab
********************
GIT_HTTP_LOW_SPEED_LIMIT=1000
GIT_HTTP_LOW_SPEED_TIME=60

# Authentication (uncomment and configure if needed)
# GITLAB_USERNAME=""
# GITLAB_TOKEN=""
# GITLAB_SSH_KEY_PATH="/home/<USER>/.ssh/id_rsa"

# Proxy settings (if needed)
# HTTP_PROXY=""
# HTTPS_PROXY=""
# NO_PROXY="localhost,127.0.0.1,gitlab.devforyou.pl"

# Repository structure
# Expected structure in controller_core repository:
# controller_core/
# ├── controller_core/          # Main ROS2 package
# ├── controller_interfaces/    # Custom interfaces
# ├── controller_msgs/          # Custom messages
# ├── controller_launch/        # Launch files
# └── controller_config/        # Configuration files

# Additional repositories to clone (optional)
ADDITIONAL_REPOS=(
    # "https://gitlab.devforyou.pl/avotech/controller_interfaces.git"
    # "https://gitlab.devforyou.pl/avotech/controller_utils.git"
)

# Branch mapping for additional repos
ADDITIONAL_REPOS_BRANCHES=(
    # "master"
    # "master"
)

# Post-clone hooks (commands to run after cloning)
POST_CLONE_HOOKS=(
    # "chmod +x controller_core/scripts/*.sh"
    # "pip3 install -r controller_core/requirements.txt"
)

# Workspace configuration
WORKSPACE_NAME="argus_controller_ws"
WORKSPACE_SRC_DIR="src"

# Build configuration
BUILD_TYPE="Release"
BUILD_PARALLEL_JOBS=4
BUILD_PACKAGES_SKIP=""
BUILD_PACKAGES_SELECT=""

# Testing configuration
RUN_TESTS=false
TEST_PACKAGES=""

# Logging
VERBOSE_CLONE=true
LOG_FILE="/var/log/argus_controller/git_operations.log"
