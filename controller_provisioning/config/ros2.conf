# ROS2 Configuration for ARGUS Controller
# Konfiguracja środowiska ROS2 dla kontrolera ARGUS

# === BASIC ROS2 SETTINGS ===

# ROS2 Distribution
ROS_DISTRO=jazzy

# Domain ID - must match with robots
# Valid range: 0-101 (avoid 0 for production)
ROS_DOMAIN_ID=42

# Network settings
# Set to 0 to allow communication between different machines
ROS_LOCALHOST_ONLY=0

# === MIDDLEWARE SETTINGS ===

# ROS2 Middleware implementation
# Options: rmw_fastrtps_cpp, rmw_cyclonedx_cpp, rmw_connextdds
RMW_IMPLEMENTATION=rmw_fastrtps_cpp

# FastRTPS specific settings
FASTRTPS_DEFAULT_PROFILES_FILE=/opt/argus_controller/config/fastrtps_profile.xml
RMW_FASTRTPS_USE_QOS_FROM_XML=0

# CycloneDX specific settings (if using CycloneDX)
# CYCLONEDX_URI=udp://localhost:7400

# === DISCOVERY SETTINGS ===

# Discovery server (leave empty for simple discovery)
ROS_DISCOVERY_SERVER=

# Discovery timeout (seconds)
ROS_DISCOVERY_TIMEOUT=30

# === LOGGING SETTINGS ===

# Logging severity levels: DEBUG, INFO, WARN, ERROR, FATAL
RCUTILS_LOGGING_SEVERITY_THRESHOLD=INFO

# Colorized output
RCUTILS_COLORIZED_OUTPUT=1

# Log format
RCUTILS_CONSOLE_OUTPUT_FORMAT="[{severity}] [{time}] [{name}]: {message}"

# === PERFORMANCE SETTINGS ===

# Memory settings
RMW_FASTRTPS_PUBLICATION_MODE=ASYNCHRONOUS
RMW_FASTRTPS_SUBSCRIPTION_MODE=ASYNCHRONOUS

# Thread settings
ROS_THREAD_POOL_SIZE=4

# === SECURITY SETTINGS ===

# ROS2 Security (SROS2) - uncomment to enable
# ROS_SECURITY_KEYSTORE=/opt/argus_controller/security/keystore
# ROS_SECURITY_ENABLE=true
# ROS_SECURITY_STRATEGY=Enforce

# === NETWORK INTERFACE SETTINGS ===

# Specific network interface (leave empty for auto-detection)
ROS_INTERFACE=

# IP address binding (leave empty for auto-detection)
ROS_IP=

# === QUALITY OF SERVICE (QOS) SETTINGS ===

# Default QoS settings for controller topics
CONTROLLER_QOS_RELIABILITY=RELIABLE
CONTROLLER_QOS_DURABILITY=VOLATILE
CONTROLLER_QOS_HISTORY=KEEP_LAST
CONTROLLER_QOS_DEPTH=10

# Robot communication QoS
ROBOT_COMM_QOS_RELIABILITY=RELIABLE
ROBOT_COMM_QOS_DURABILITY=TRANSIENT_LOCAL
ROBOT_COMM_QOS_HISTORY=KEEP_LAST
ROBOT_COMM_QOS_DEPTH=100

# === TOPIC SETTINGS ===

# Controller topics
CONTROLLER_STATUS_TOPIC=/controller/status
CONTROLLER_COMMAND_TOPIC=/controller/command
ROBOT_LIST_TOPIC=/controller/robots
ROBOT_STATUS_TOPIC=/robot/+/status
ROBOT_COMMAND_TOPIC=/robot/+/command

# === SERVICE SETTINGS ===

# Controller services
ROBOT_DISCOVERY_SERVICE=/controller/discover_robots
ROBOT_CONNECT_SERVICE=/controller/connect_robot
ROBOT_DISCONNECT_SERVICE=/controller/disconnect_robot

# === ACTION SETTINGS ===

# Controller actions
MISSION_EXECUTE_ACTION=/controller/execute_mission
ROBOT_NAVIGATE_ACTION=/robot/+/navigate

# === PARAMETER SETTINGS ===

# Controller parameters
CONTROLLER_NAME=argus_controller
CONTROLLER_VERSION=1.0.0
MAX_ROBOTS=10
HEARTBEAT_INTERVAL=5.0
CONNECTION_TIMEOUT=30.0

# Robot parameters
ROBOT_DISCOVERY_TIMEOUT=10.0
ROBOT_CONNECTION_RETRY=3
ROBOT_HEARTBEAT_TIMEOUT=15.0

# === MONITORING SETTINGS ===

# Enable monitoring
ENABLE_MONITORING=true
MONITORING_INTERVAL=1.0

# Metrics topics
METRICS_TOPIC=/controller/metrics
DIAGNOSTICS_TOPIC=/controller/diagnostics

# === DEVELOPMENT SETTINGS ===

# Debug mode
DEBUG_MODE=false

# Simulation mode
SIMULATION_MODE=false

# Mock robots (for testing without real robots)
MOCK_ROBOTS=false
MOCK_ROBOT_COUNT=2

# === BACKUP AND RECOVERY ===

# Configuration backup
CONFIG_BACKUP_ENABLED=true
CONFIG_BACKUP_INTERVAL=3600
CONFIG_BACKUP_PATH=/opt/argus_controller/backups

# === INTEGRATION SETTINGS ===

# Database connection
DATABASE_ENABLED=true
DATABASE_TYPE=sqlite
DATABASE_PATH=/root/argus_controller/data/controller.db

# Web API (headless interface)
WEB_API_ENABLED=true
WEB_API_PORT=8080
WEB_API_HOST=0.0.0.0

# WebSocket for real-time communication
WEBSOCKET_ENABLED=true
WEBSOCKET_PORT=8081

# REST API endpoints
REST_API_ENABLED=true
REST_API_PORT=3000

# === CUSTOM SETTINGS ===

# Add your custom ROS2 environment variables here
# CUSTOM_VAR=value
