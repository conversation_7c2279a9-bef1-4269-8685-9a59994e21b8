# P<PERSON><PERSON><PERSON><PERSON> Konfiguracji Środowisk

Przykłady konfiguracji kontrolera ARGUS dla różnych środowisk.

## 1. Środowisko Laboratoryjne

### Charakterystyka
- Stabilna sieć LAN
- Wiele robotów (5-10)
- Raspberry Pi 4/5 z Ubuntu Server (headless)
- Monitoring przez Web API i SSH

### Konfiguracja ROS2 (ros2.conf)
```bash
ROS_DOMAIN_ID=42
ROS_LOCALHOST_ONLY=0
RMW_IMPLEMENTATION=rmw_fastrtps_cpp
RCUTILS_LOGGING_SEVERITY_THRESHOLD=INFO
MAX_ROBOTS=10
HEARTBEAT_INTERVAL=2.0
CONNECTION_TIMEOUT=15.0
```

### Konfiguracja sieci (netplan)
```yaml
network:
  version: 2
  renderer: NetworkManager
  ethernets:
    eth0:
      addresses:
        - *************/24
      gateway4: ***********
      nameservers:
        addresses: [*******, *******]
```

### Firewall
```bash
# Otwarte porty dla laboratorium
ufw allow from ***********/24 to any port 8080
ufw allow from ***********/24 to any port 3000
ufw allow 7400:7500/udp
```

## 2. Środowisko Produkcyjne

### Charakterystyka
- Wysoka niezawodność
- Bezpieczeństwo
- Automatyczne backupy
- Monitoring 24/7

### Konfiguracja ROS2 (ros2.conf)
```bash
ROS_DOMAIN_ID=1
ROS_LOCALHOST_ONLY=0
RMW_IMPLEMENTATION=rmw_fastrtps_cpp
RCUTILS_LOGGING_SEVERITY_THRESHOLD=WARN
MAX_ROBOTS=20
HEARTBEAT_INTERVAL=5.0
CONNECTION_TIMEOUT=30.0
ENABLE_MONITORING=true
CONFIG_BACKUP_ENABLED=true
CONFIG_BACKUP_INTERVAL=1800
```

### Bezpieczeństwo
```bash
# Firewall restrykcyjny
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow from 10.0.0.0/8 to any port 8080
ufw allow from 10.0.0.0/8 to any port 3000
```

### Monitoring
```bash
# Systemd timer dla backupów
[Unit]
Description=ARGUS Controller Backup
Requires=controller_core.service

[Timer]
OnCalendar=*-*-* 02:00:00
Persistent=true

[Install]
WantedBy=timers.target
```

## 3. Środowisko Deweloperskie

### Charakterystyka
- Szybkie iteracje
- Debugowanie
- Symulacja robotów
- Elastyczna konfiguracja

### Konfiguracja ROS2 (ros2.conf)
```bash
ROS_DOMAIN_ID=99
ROS_LOCALHOST_ONLY=1
RMW_IMPLEMENTATION=rmw_fastrtps_cpp
RCUTILS_LOGGING_SEVERITY_THRESHOLD=DEBUG
DEBUG_MODE=true
SIMULATION_MODE=true
MOCK_ROBOTS=true
MOCK_ROBOT_COUNT=3
```

### Docker Compose dla symulacji
```yaml
version: '3.8'
services:
  controller:
    build: .
    ports:
      - "8080:8080"
      - "3000:3000"
    environment:
      - ROS_DOMAIN_ID=99
      - DEBUG_MODE=true
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
      
  robot-sim-1:
    image: argus/robot-simulator
    environment:
      - ROS_DOMAIN_ID=99
      - ROBOT_ID=robot_001
      
  robot-sim-2:
    image: argus/robot-simulator
    environment:
      - ROS_DOMAIN_ID=99
      - ROBOT_ID=robot_002
```

## 4. Środowisko Chmurowe (Cloud)

### Charakterystyka
- Skalowalne
- Dostęp zdalny
- Load balancing
- Auto-scaling

### Konfiguracja Kubernetes
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: argus-controller
spec:
  replicas: 2
  selector:
    matchLabels:
      app: argus-controller
  template:
    metadata:
      labels:
        app: argus-controller
    spec:
      containers:
      - name: controller
        image: argus/controller:latest
        ports:
        - containerPort: 8080
        - containerPort: 3000
        env:
        - name: ROS_DOMAIN_ID
          value: "42"
        - name: DATABASE_TYPE
          value: "postgresql"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
```

### Load Balancer
```yaml
apiVersion: v1
kind: Service
metadata:
  name: argus-controller-service
spec:
  selector:
    app: argus-controller
  ports:
  - name: api
    port: 80
    targetPort: 8080
  - name: web
    port: 3000
    targetPort: 3000
  type: LoadBalancer
```

## 5. Środowisko Edge Computing (Raspberry Pi)

### Charakterystyka
- Raspberry Pi 4/5 (headless)
- Ograniczone zasoby
- Lokalne przetwarzanie
- Offline capability
- Zarządzanie przez SSH i Web API

### Konfiguracja dla Raspberry Pi
```bash
# Optymalizacja dla ARM
ROS_DOMAIN_ID=42
ROS_LOCALHOST_ONLY=0
RMW_IMPLEMENTATION=rmw_cyclonedx_cpp  # Lżejszy middleware
RCUTILS_LOGGING_SEVERITY_THRESHOLD=WARN
MAX_ROBOTS=5
HEARTBEAT_INTERVAL=10.0
CONNECTION_TIMEOUT=60.0

# Ograniczenia zasobów
ROS_THREAD_POOL_SIZE=2
BUILD_PARALLEL_JOBS=2
```

### Systemd dla Raspberry Pi
```ini
[Unit]
Description=ARGUS Controller Edge
After=network.target
StartLimitIntervalSec=0

[Service]
Type=simple
Restart=always
RestartSec=5
User=pi
ExecStart=/home/<USER>/argus_controller/bin/controller_edge
MemoryLimit=1G
CPUQuota=80%

[Install]
WantedBy=multi-user.target
```

## 6. Środowisko Testowe (CI/CD)

### Charakterystyka
- Automatyczne testy
- Integracja ciągła
- Izolowane środowisko
- Szybkie deployment

### GitHub Actions
```yaml
name: ARGUS Controller CI
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-24.04
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup ROS2
      uses: ros-tooling/setup-ros@v0.6
      with:
        required-ros-distributions: jazzy
        
    - name: Install dependencies
      run: |
        sudo apt update
        sudo apt install -y python3-colcon-common-extensions
        
    - name: Build workspace
      run: |
        source /opt/ros/jazzy/setup.bash
        colcon build --packages-select controller_core
        
    - name: Run tests
      run: |
        source /opt/ros/jazzy/setup.bash
        source install/setup.bash
        colcon test --packages-select controller_core
        colcon test-result --verbose
```

### Docker dla testów
```dockerfile
FROM ros:jazzy-desktop

# Install dependencies
RUN apt-get update && apt-get install -y \
    python3-colcon-common-extensions \
    python3-pytest \
    && rm -rf /var/lib/apt/lists/*

# Copy source code
COPY . /workspace/src/controller_core

# Build and test
WORKDIR /workspace
RUN source /opt/ros/jazzy/setup.bash && \
    colcon build && \
    colcon test && \
    colcon test-result --verbose

CMD ["bash"]
```

## 7. Konfiguracja Multi-Site

### Charakterystyka
- Wiele lokalizacji
- VPN connectivity
- Centralne zarządzanie
- Synchronizacja danych

### VPN Configuration (WireGuard)
```ini
[Interface]
PrivateKey = <controller-private-key>
Address = ********/24
ListenPort = 51820

[Peer]
# Site A
PublicKey = <site-a-public-key>
AllowedIPs = ********/24
Endpoint = site-a.example.com:51820

[Peer]
# Site B
PublicKey = <site-b-public-key>
AllowedIPs = ********/24
Endpoint = site-b.example.com:51820
```

### ROS2 Multi-Site
```bash
# Site-specific domain IDs
SITE_A_DOMAIN_ID=10
SITE_B_DOMAIN_ID=20
CENTRAL_DOMAIN_ID=1

# Bridge configuration
ros2 run domain_bridge domain_bridge \
  --from-domain $SITE_A_DOMAIN_ID \
  --to-domain $CENTRAL_DOMAIN_ID \
  --topic /robot/+/status
```

## Wybór Konfiguracji

### Kryteria wyboru:
1. **Liczba robotów**: < 5 (Edge), 5-10 (Lab), > 10 (Production/Cloud)
2. **Niezawodność**: Deweloperskie < Laboratoryjne < Produkcyjne
3. **Zasoby**: Raspberry Pi (Edge) < PC (Lab) < Server (Production)
4. **Bezpieczeństwo**: Deweloperskie < Laboratoryjne < Produkcyjne
5. **Skalowność**: Edge < Lab < Production < Cloud

### Migracja między środowiskami:
```bash
# Backup konfiguracji
./scripts/backup-config.sh --environment lab

# Przełączenie na produkcję
./scripts/switch-environment.sh --from lab --to production

# Weryfikacja
controller-diag --environment production
```
