# ARGUS Controller - Przydatne Komendy

Zbiór przydatnych komend do zarządzania kontrolerem ARGUS na Ubuntu 24.04 Server (headless).

## Dostęp Zdalny

### SSH
```bash

# ssh
sudo systemctl edit --full ssh

[Service]
ExecStart=/usr/sbin/sshd -D $SSHD_OPTS -o "PasswordAuthentication yes"

ssh-copy-id ubuntu@*************


# Połączenie SSH do kontrolera
ssh username@controller-ip-address

# Połączenie z przekierowaniem portów (dla Web API)
ssh -L 8080:localhost:8080 -L 3000:localhost:3000 username@controller-ip-address

# Kopiowanie plików przez SCP
scp file.txt username@controller-ip-address:~/argus_controller/
scp -r local_dir/ username@controller-ip-address:~/argus_controller/
```

### Web API
```bash
# Test API przez curl
curl http://controller-ip:8080/api/status
curl http://controller-ip:8080/api/robots
curl -X POST http://controller-ip:8080/api/robot/connect -d '{"robot_id": "robot_001"}'

# WebSocket test
wscat -c ws://controller-ip:8081/ws
```

## Instalacja i Konfiguracja

### Instalacja kontrolera
```bash
# Pełna instalacja
sudo ./install.sh

# Sprawdzenie wymagań systemowych
sudo ./install.sh --check

# Instalacja bez automatycznego restartu
sudo ./install.sh --no-reboot

# Kontynuacja instalacji od określonego skryptu
sudo ./install.sh --continue-from 04-ros2.sh
```

### Czyszczenie systemu
```bash
# Wyczyść workspace ROS2
sudo ./scripts/cleanup.sh --workspace

# Wyczyść logi
sudo ./scripts/cleanup.sh --logs

# Standardowe czyszczenie (workspace, logi, cache)
sudo ./scripts/cleanup.sh --all

# UWAGA: Pełne czyszczenie (usuwa wszystkie dane!)
sudo ./scripts/cleanup.sh --full
```

## Zarządzanie Usługami

### Kontroler Core
```bash
# Status usługi
systemctl status controller_core

# Start/stop/restart
sudo systemctl start controller_core
sudo systemctl stop controller_core
sudo systemctl restart controller_core

# Włącz/wyłącz autostart
sudo systemctl enable controller_core
sudo systemctl disable controller_core

# Logi usługi
sudo journalctl -u controller_core -f
sudo journalctl -u controller_core --since "1 hour ago"
```

### Zarządzanie przez skrypt
```bash
# Skrypt zarządzania (po instalacji)
controller-manage start
controller-manage stop
controller-manage restart
controller-manage status
controller-manage logs
controller-manage build
controller-manage clean
controller-manage update
controller-manage diag
```

## ROS2 Commands

### Podstawowe komendy ROS2
```bash
# Sprawdź wersję ROS2
ros2 --version

# Lista węzłów
ros2 node list

# Lista topików
ros2 topic list

# Lista serwisów
ros2 service list

# Informacje o węźle
ros2 node info /controller_node

# Echo topiku
ros2 topic echo /controller/status

# Publikowanie na topik
ros2 topic pub /controller/command std_msgs/String "data: 'test'"
```

### Workspace ROS2
```bash
# Przejdź do workspace (w /root jak robot_provisioning)
cd /root/argus_controller_ws

# Aktywuj środowisko ROS2
source /opt/ros/jazzy/setup.bash
source install/setup.bash

# Buduj workspace
colcon build

# Buduj z debugowaniem
colcon build --cmake-args -DCMAKE_BUILD_TYPE=Debug

# Buduj tylko określone pakiety
colcon build --packages-select controller_core

# Buduj z testami
colcon build --cmake-args -DBUILD_TESTING=ON
colcon test
```

### Diagnostyka ROS2
```bash
# Sprawdź zmienne środowiskowe ROS2
printenv | grep ROS

# Test komunikacji ROS2
ros2 run demo_nodes_cpp talker &
ros2 run demo_nodes_py listener

# Sprawdź QoS topiku
ros2 topic info /controller/status --verbose

# Monitor wydajności
ros2 run rqt_top rqt_top
```

## Sieć i Komunikacja

### Status sieci
```bash
# Interfejsy sieciowe
ip addr show

# Status NetworkManager
systemctl status NetworkManager
nmcli connection show

# Status Avahi (mDNS)
systemctl status avahi-daemon
avahi-browse -t _argus-controller._tcp

# Test portów
netstat -tlnp | grep -E "(8080|8081|3000|7400)"
ss -tlnp | grep -E "(8080|8081|3000|7400)"
```

### Firewall
```bash
# Status firewall
sudo ufw status numbered

# Dodaj regułę
sudo ufw allow 8080/tcp

# Usuń regułę
sudo ufw delete 1

# Reset firewall
sudo ufw --force reset
```

### Testowanie komunikacji
```bash
# Test połączenia z robotem
ping robot-ip-address

# Test portów ROS2
nc -zv robot-ip-address 7400-7500

# Test multicast
ping -c 3 *********
```

## Monitoring i Diagnostyka

### Diagnostyka systemu
```bash
# Skrypt diagnostyczny
controller-diag

# Lub bezpośrednio
/opt/argus_controller/bin/controller_diagnostics.sh
```

### Monitoring zasobów
```bash
# CPU i pamięć
htop
top

# Dysk
df -h
du -sh ~/argus_controller/*

# Sieć
iftop
nload
nethogs
```

### Logi systemowe
```bash
# Logi kontrolera
sudo tail -f /var/log/argus_controller/*.log

# Logi systemowe
sudo journalctl -f
sudo journalctl -u controller_core -f

# Logi sieciowe
sudo journalctl -u NetworkManager -f
sudo journalctl -u avahi-daemon -f
```

## Rozwój i Debugowanie

### Git i kod źródłowy
```bash
# Aktualizacja kodu źródłowego
cd ~/argus_controller/src/controller_core
git pull
git status
git log --oneline -10

# Przełączenie na inną gałąź
git checkout develop
git checkout -b feature/new-feature
```

### Python i zależności
```bash
# Aktywacja wirtualnego środowiska
source ~/argus_controller/venv/bin/activate

# Instalacja pakietów Python
pip install package_name
pip install -r requirements.txt

# Lista zainstalowanych pakietów
pip list
pip freeze > requirements.txt
```

### Node.js i aplikacje web
```bash
# Sprawdź wersję Node.js
node --version
npm --version

# Instalacja pakietów
npm install
npm install -g package_name

# Uruchomienie aplikacji web
cd ~/argus_controller/src/controller_view
npm start
npm run build
```

### Docker (jeśli używany)
```bash
# Lista kontenerów
docker ps -a

# Logi kontenera
docker logs container_name

# Wejście do kontenera
docker exec -it container_name bash

# Czyszczenie Docker
docker system prune -f
```

## Backup i Przywracanie

### Backup konfiguracji
```bash
# Backup katalogu kontrolera
tar -czf argus_controller_backup_$(date +%Y%m%d).tar.gz ~/argus_controller/

# Backup tylko konfiguracji
tar -czf config_backup_$(date +%Y%m%d).tar.gz ~/argus_controller/config/

# Backup bazy danych
cp ~/argus_controller/data/controller.db ~/argus_controller/backups/
```

### Przywracanie
```bash
# Przywracanie z backup
tar -xzf argus_controller_backup_20240101.tar.gz -C ~/

# Przywracanie konfiguracji
tar -xzf config_backup_20240101.tar.gz -C ~/argus_controller/
```

## Rozwiązywanie Problemów

### Problemy z ROS2
```bash
# Restart daemona ROS2
ros2 daemon stop
ros2 daemon start

# Wyczyść cache ROS2
rm -rf ~/.ros/log/*

# Sprawdź zmienne środowiskowe
echo $ROS_DOMAIN_ID
echo $ROS_LOCALHOST_ONLY
echo $RMW_IMPLEMENTATION
```

### Problemy z siecią
```bash
# Restart NetworkManager
sudo systemctl restart NetworkManager

# Restart Avahi
sudo systemctl restart avahi-daemon

# Flush DNS
sudo systemd-resolve --flush-caches

# Reset netplan
sudo netplan apply
```

### Problemy z usługami
```bash
# Sprawdź status wszystkich usług
systemctl --failed

# Restart usług systemowych
sudo systemctl restart controller_core
sudo systemctl restart avahi-daemon
sudo systemctl restart NetworkManager

# Przeładuj konfigurację systemd
sudo systemctl daemon-reload
```

## Aktualizacja Systemu

### Aktualizacja Ubuntu
```bash
# Aktualizacja pakietów
sudo apt update
sudo apt upgrade

# Aktualizacja do nowej wersji (ostrożnie!)
sudo do-release-upgrade
```

### Aktualizacja ROS2
```bash
# Aktualizacja pakietów ROS2
sudo apt update
sudo apt upgrade ros-jazzy-*

# Przebudowanie workspace po aktualizacji
cd ~/argus_controller/workspace
colcon build --cmake-clean-cache
```

## Przydatne Aliasy

Po instalacji dostępne są następujące aliasy:

```bash
# Nawigacja
controller-ws          # cd /root/argus_controller_ws
controller-src         # cd /root/argus_controller/src
controller-logs        # cd /root/argus_controller/logs
controller-config      # cd /root/argus_controller/config

# ROS2
controller-build       # colcon build w workspace
controller-source      # source setup.bash

# Zarządzanie
controller-status      # status usługi
controller-restart     # restart usługi
controller-logs        # logi usługi
controller-diag        # diagnostyka
```
