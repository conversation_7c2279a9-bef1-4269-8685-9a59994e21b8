#!/bin/bash
# network-status.sh - Sprawdzanie statusu sieci

echo "=== ARGUS Robot - Status Sieci ==="
echo "Data: $(date)"
echo ""

# Status NetworkManager
echo "1. Status NetworkManager:"
echo "========================="
if systemctl is-active --quiet NetworkManager; then
    echo "✓ NetworkManager jest aktywny"
else
    echo "✗ NetworkManager nie jest aktywny"
fi
echo ""

# Status interfejsów
echo "2. Status interfejsów sieciowych:"
echo "================================="
nmcli dev status
echo ""

# Aktywne połączenia
echo "3. Aktywne połączenia:"
echo "======================"
nmcli con show --active
echo ""

# IP adresy
echo "4. Adresy IP:"
echo "============="
echo "wlan0:"
ip addr show wlan0 | grep "inet " | awk '{print "  " $2}' || echo "  Brak IP"
echo "eth0:"
ip addr show eth0 | grep "inet " | awk '{print "  " $2}' || echo "  Brak IP"
echo ""

# Test połączenia internetowego
echo "5. Test połączenia internetowego:"
echo "=================================="
if ping -c 1 -W 3 ******* >/dev/null 2>&1; then
    echo "✓ Połączenie z internetem działa"
else
    echo "✗ Brak połączenia z internetem"
fi
echo ""

# Dostępne sieci WiFi (tylko pierwsze 10)
echo "6. Dostępne sieci WiFi (pierwsze 10):"
echo "======================================"
nmcli dev wifi list | head -11
echo ""

# Routing
echo "7. Tabela routingu:"
echo "==================="
ip route show
echo ""

# DNS
echo "8. Konfiguracja DNS:"
echo "===================="
if [ -f /etc/resolv.conf ]; then
    grep nameserver /etc/resolv.conf | head -3
else
    echo "Brak pliku /etc/resolv.conf"
fi
echo ""

# ROS2 Environment
echo "9. Środowisko ROS2:"
echo "==================="
echo "ROS_DOMAIN_ID: ${ROS_DOMAIN_ID:-'nie ustawione'}"
echo "ROS_LOCALHOST_ONLY: ${ROS_LOCALHOST_ONLY:-'nie ustawione'}"
echo "RMW_IMPLEMENTATION: ${RMW_IMPLEMENTATION:-'nie ustawione'}"
echo ""

# Porty nasłuchujące
echo "10. Aktywne porty sieciowe:"
echo "==========================="
netstat -tlnp 2>/dev/null | grep -E ":(22|8080|8554|8081)" | head -5 || echo "Brak aktywnych portów"

echo ""
echo "=== Koniec raportu ==="
