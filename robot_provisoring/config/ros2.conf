# ROS2 Configuration for ARGUS Robot
# Konfiguracja środowiska ROS2 dla robota ARGUS

# === BASIC ROS2 SETTINGS ===

# ROS2 Distribution
ROS_DISTRO=jazzy

# Domain ID - must match with controller
# Valid range: 0-101 (avoid 0 for production)
ROS_DOMAIN_ID=42

# Network settings
# Set to 0 to allow communication with controller
ROS_LOCALHOST_ONLY=0

# === MIDDLEWARE SETTINGS ===

# ROS2 Middleware implementation - MUST match controller
# FastRTPS for compatibility with controller
RMW_IMPLEMENTATION=rmw_fastrtps_cpp

# FastRTPS specific settings
FASTRTPS_DEFAULT_PROFILES_FILE=/opt/argusik/config/fastrtps_profile.xml
RMW_FASTRTPS_USE_QOS_FROM_XML=0

# === DISCOVERY SETTINGS ===

# Discovery server (leave empty for simple discovery)
ROS_DISCOVERY_SERVER=

# Discovery timeout (seconds)
ROS_DISCOVERY_TIMEOUT=30

# === LOGGING SETTINGS ===

# Logging severity levels: DEBUG, INF<PERSON>, WARN, ERROR, FATAL
RCUTILS_LOGGING_SEVERITY_THRESHOLD=INFO

# Colorized output
RCUTILS_COLORIZED_OUTPUT=1

# Log format
RCUTILS_CONSOLE_OUTPUT_FORMAT="[{severity}] [{time}] [{name}]: {message}"

# === PERFORMANCE SETTINGS ===

# Memory settings
RMW_FASTRTPS_PUBLICATION_MODE=ASYNCHRONOUS
RMW_FASTRTPS_SUBSCRIPTION_MODE=ASYNCHRONOUS

# Thread settings (limited for Raspberry Pi)
ROS_THREAD_POOL_SIZE=2

# === ROBOT SETTINGS ===

# Robot identification
ROBOT_ID=argus_robot_001
ROBOT_TYPE=mobile_robot
ROBOT_VERSION=1.0.0

# Robot capabilities
ROBOT_HAS_CAMERA=true
ROBOT_HAS_LIDAR=false
ROBOT_HAS_IMU=true
ROBOT_HAS_GPS=false

# === QUALITY OF SERVICE (QOS) SETTINGS ===

# Default QoS settings for robot topics
ROBOT_QOS_RELIABILITY=RELIABLE
ROBOT_QOS_DURABILITY=VOLATILE
ROBOT_QOS_HISTORY=KEEP_LAST
ROBOT_QOS_DEPTH=10

# Sensor data QoS (może być BEST_EFFORT dla wydajności)
SENSOR_QOS_RELIABILITY=BEST_EFFORT
SENSOR_QOS_DURABILITY=VOLATILE
SENSOR_QOS_HISTORY=KEEP_LAST
SENSOR_QOS_DEPTH=5

# Command QoS (musi być RELIABLE)
COMMAND_QOS_RELIABILITY=RELIABLE
COMMAND_QOS_DURABILITY=TRANSIENT_LOCAL
COMMAND_QOS_HISTORY=KEEP_LAST
COMMAND_QOS_DEPTH=1

# === TOPIC SETTINGS ===

# Robot status topics
ROBOT_STATUS_TOPIC=/robot/status
ROBOT_HEARTBEAT_TOPIC=/robot/heartbeat
ROBOT_DIAGNOSTICS_TOPIC=/robot/diagnostics

# Sensor topics
CAMERA_TOPIC=/robot/camera/image_raw
IMU_TOPIC=/robot/imu/data
ODOM_TOPIC=/robot/odom
BATTERY_TOPIC=/robot/battery

# Command topics
CMD_VEL_TOPIC=/robot/cmd_vel
CMD_MISSION_TOPIC=/robot/cmd_mission

# === SERVICE SETTINGS ===

# Robot services
ROBOT_INFO_SERVICE=/robot/get_info
ROBOT_CALIBRATE_SERVICE=/robot/calibrate
ROBOT_EMERGENCY_STOP_SERVICE=/robot/emergency_stop

# === ACTION SETTINGS ===

# Robot actions
NAVIGATE_ACTION=/robot/navigate
DOCK_ACTION=/robot/dock
MISSION_ACTION=/robot/execute_mission

# === PARAMETER SETTINGS ===

# Robot parameters
ROBOT_NAME=argus_robot
ROBOT_NAMESPACE=robot
MAX_LINEAR_VELOCITY=1.0
MAX_ANGULAR_VELOCITY=1.57
WHEEL_SEPARATION=0.3
WHEEL_RADIUS=0.1

# Communication parameters
HEARTBEAT_INTERVAL=5.0
STATUS_PUBLISH_RATE=10.0
SENSOR_PUBLISH_RATE=30.0

# === HARDWARE SETTINGS ===

# GPIO and hardware interfaces
ENABLE_GPIO=true
ENABLE_I2C=true
ENABLE_SPI=true
ENABLE_CAMERA=true

# Camera settings
CAMERA_WIDTH=640
CAMERA_HEIGHT=480
CAMERA_FPS=30

# Motor settings
MOTOR_PWM_FREQUENCY=1000
MOTOR_ENCODER_PPR=1024

# === MONITORING SETTINGS ===

# Enable monitoring
ENABLE_MONITORING=true
MONITORING_INTERVAL=1.0

# Metrics topics
METRICS_TOPIC=/robot/metrics
DIAGNOSTICS_TOPIC=/robot/diagnostics

# === DEVELOPMENT SETTINGS ===

# Debug mode
DEBUG_MODE=false

# Simulation mode (for testing without hardware)
SIMULATION_MODE=false

# === BACKUP AND RECOVERY ===

# Configuration backup
CONFIG_BACKUP_ENABLED=true
CONFIG_BACKUP_INTERVAL=3600
CONFIG_BACKUP_PATH=/opt/argusik/backups

# === INTEGRATION SETTINGS ===

# Controller connection
CONTROLLER_HOST=auto_discover
CONTROLLER_PORT=8080
CONTROLLER_TIMEOUT=30

# Database (local SQLite for robot data)
DATABASE_ENABLED=true
DATABASE_TYPE=sqlite
DATABASE_PATH=/opt/argusik/data/robot.db

# === CUSTOM SETTINGS ===

# Add your custom ROS2 environment variables here
# CUSTOM_VAR=value
