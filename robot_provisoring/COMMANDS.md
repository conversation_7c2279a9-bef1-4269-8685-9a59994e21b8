# ARGUS Robot - Przydatne komendy

## Instal<PERSON>ja

```bash
# Pelna instalacja (zalecane)
sudo ./install.sh

```

## Zarzad<PERSON>ie uslugami

```bash
# Status glownej uslugi
sudo systemctl status argus_core

# Start/Stop/Restart
sudo systemctl start argus_core
sudo systemctl stop argus_core
sudo systemctl restart argus_core

# Wlaczenie/wylaczenie autostartu
sudo systemctl enable argus_core
sudo systemctl disable argus_core

# Logi uslugi
sudo journalctl -u argus_core -f
sudo journalctl -u argus_core -n 50
```

## ROS2

```bash
# Source srodowiska (jako root)
source /opt/ros/jazzy/setup.bash
source /root/ros2_jazzy/install/setup.bash

# Ladowanie konfiguracji ROS2
source /opt/argusik/bin/load_ros2_config.sh

# Sprawdzenie zmiennych srodowiskowych
echo "ROS_DOMAIN_ID: $ROS_DOMAIN_ID"
echo "ROS_LOCALHOST_ONLY: $ROS_LOCALHOST_ONLY"
echo "RMW_IMPLEMENTATION: $RMW_IMPLEMENTATION"

# Lista wezlow
ros2 node list

# Lista topikow
ros2 topic list

# Informacje o topiku
ros2 topic info /camera/image_raw

# Echo topiku
ros2 topic echo /sensors/temperature

# Budowanie workspace
cd /root/ros2_jazzy
colcon build --symlink-install
```

## Zarzadzanie kodem zrodlowym

```bash
# Aktualizacja kodu z GitLab
cd /root/src_ros2/robot_core
git pull

# Sprawdzenie statusu repozytorium
git status
git log --oneline -10

# Przelaczenie na inny branch
git checkout develop

# Rebuild po zmianach w kodzie
cd /root/ros2_jazzy
colcon build --symlink-install

# Sprawdzenie struktury katalogow
ls -la /root/src_ros2/
ls -la /root/ros2_jazzy/src/
```

## Diagnostyka

```bash
# Skrypt diagnostyczny
sudo /opt/argusik/bin/argus_diagnostics.sh

# Status sieci
ip addr show
iwconfig

# Status kamer
v4l2-ctl --list-devices
ls -la /dev/video*

# Temperatura CPU
vcgencmd measure_temp

# Status throttling
vcgencmd get_throttled

# Uzycie dysku
df -h

# Procesy
htop
ps aux | grep ros
```

## Siec

```bash


# Status Access Point
sudo systemctl status hostapd
sudo systemctl status dnsmasq

# Restart sieci
sudo systemctl restart hostapd
sudo systemctl restart dnsmasq

# Konfiguracja IP
sudo ip addr add ***********/24 dev wlan0

# Skanowanie sieci WiFi
sudo iwlist wlan0 scan | grep ESSID
```

## Logi

```bash
# Logi ARGUS
tail -f /var/log/argusik/*.log
ls -la /var/log/argusik/

# Logi systemowe
sudo journalctl -f
sudo journalctl -u argus_core
sudo journalctl -u hostapd
sudo journalctl -u dnsmasq

# Logi kernela
dmesg | tail
```

## Backup i restore

```bash
# Backup konfiguracji
sudo tar -czf argus_backup.tar.gz /root/ros2_jazzy /root/src_ros2 /opt/argusik /etc/argusik

# Backup obrazu SD (z innego komputera)
sudo dd if=/dev/sdX of=argus_robot_backup.img bs=4M status=progress

# Restore obrazu SD
sudo dd if=argus_robot_backup.img of=/dev/sdX bs=4M status=progress
```

## Aktualizacja

```bash
# Aktualizacja systemu
sudo apt update && sudo apt upgrade -y

# Aktualizacja ROS2 pakietow
sudo apt update && sudo apt upgrade ros-jazzy-*

# Rebuild workspace po zmianach
cd /root/ros2_jazzy
rm -rf build install log
colcon build --symlink-install
```

## Rozwiazywanie problemow

```bash
# Reset konfiguracji sieci
sudo systemctl stop hostapd dnsmasq
sudo systemctl start hostapd dnsmasq

# Reset ROS2 workspace
cd /root/ros2_jazzy
rm -rf build install log
source /opt/ros/jazzy/setup.bash
colcon build

# Sprawdzenie bledow w logach
sudo journalctl -p err
grep -r "ERROR" /var/log/argusik/
```

## Komunikacja z kontrolerem

```bash
# Sprawdzenie konfiguracji ROS2 dla komunikacji
echo "ROS_DOMAIN_ID: $ROS_DOMAIN_ID"
echo "ROS_LOCALHOST_ONLY: $ROS_LOCALHOST_ONLY"
echo "RMW_IMPLEMENTATION: $RMW_IMPLEMENTATION"

# Test komunikacji z kontrolerem
ros2 topic list | grep controller
ros2 node list | grep controller

# Publikowanie statusu robota
ros2 topic pub /robot/status std_msgs/String "data: 'robot_online'" --once

# Nasłuchiwanie komend z kontrolera
ros2 topic echo /robot/cmd_vel

# Test autodiscovery kontrolera
avahi-browse -t _argus-controller._tcp

# Sprawdzenie połączenia sieciowego z kontrolerem
ping controller-ip-address
nc -zv controller-ip-address 8080

# Reset do ustawien fabrycznych
sudo systemctl stop argus_core
sudo rm -rf /root/ros2_jazzy/build /root/ros2_jazzy/install
sudo rm -rf /root/src_ros2/robot_core
sudo ./install.sh
```
