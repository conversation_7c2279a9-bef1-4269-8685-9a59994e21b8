[Unit]
Description=ARGUS Robot Core Service
After=network.target multi-user.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/root/ros2_jazzy
Environment="ROS_DOMAIN_ID=42"
Environment="ROS_LOCALHOST_ONLY=0"
Environment="RMW_IMPLEMENTATION=rmw_fastrtps_cpp"
ExecStart=/bin/bash -c "source /root/ros2_jazzy/install/setup.bash && ros2 launch robot_core argus_robot.launch.py"
Restart=always
RestartSec=5
StandardOutput=append:/var/log/argusik/argus_core.log
StandardError=append:/var/log/argusik/argus_core_error.log
SyslogIdentifier=argus_core

[Install]
WantedBy=multi-user.target